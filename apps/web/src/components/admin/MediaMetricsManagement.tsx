"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { trpc } from "@/utils/trpc";
import { MetricInput } from "@/components/admin/forms/fields/MetricInput";
import {
  Plus,
  Edit3,
  Trash2,
  Save,
  X,
  ExternalLink,
  Users,
  Eye,
  BarChart3,
  ArrowUp,
  ArrowDown
} from "lucide-react";
import { ConfirmDialog } from "@/components/ui/confirm-dialog";

interface MediaMetric {
  id: string;
  account_handle: string;
  display_name: string;
  description: string | null;
  profile_image_url: string | null;
  total_impressions: number;
  total_views: number;
  total_followers: number;
  is_active: boolean;
  display_order: number;
  created_at: string;
  updated_at: string;
}

interface EditingMetric extends Partial<MediaMetric> {
  id?: string;
}

export function MediaMetricsManagement() {
  const [metrics, setMetrics] = useState<MediaMetric[]>([]);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingData, setEditingData] = useState<EditingMetric>({});
  const [deleteConfirm, setDeleteConfirm] = useState<{ id: string; name: string } | null>(null);

  // Queries and mutations
  const { data: allMetrics, isLoading, refetch } = trpc.mediaMetrics.getAllMediaMetrics.useQuery();
  
  const updateMutation = trpc.mediaMetrics.updateMediaMetric.useMutation({
    onSuccess: () => {
      toast.success("Metric updated successfully");
      refetch();
      setEditingId(null);
      setEditingData({});
    },
    onError: (error) => {
      toast.error(`Failed to update metric: ${error.message}`);
    },
  });

  const createMutation = trpc.mediaMetrics.createMediaMetric.useMutation({
    onSuccess: () => {
      toast.success("Media account created successfully");
      refetch();
      setShowAddForm(false);
      setEditingData({});
    },
    onError: (error) => {
      toast.error(`Failed to create media account: ${error.message}`);
    },
  });

  const deleteMutation = trpc.mediaMetrics.deleteMediaMetric.useMutation({
    onSuccess: () => {
      toast.success("Media account deleted successfully");
      refetch();
      setDeleteConfirm(null);
    },
    onError: (error) => {
      toast.error(`Failed to delete media account: ${error.message}`);
    },
  });

  const toggleActiveMutation = trpc.mediaMetrics.toggleActive.useMutation({
    onSuccess: () => {
      toast.success("Status updated successfully");
      refetch();
    },
    onError: (error) => {
      toast.error(`Failed to update status: ${error.message}`);
    },
  });

  const updateOrderMutation = trpc.mediaMetrics.updateDisplayOrder.useMutation({
    onSuccess: () => {
      toast.success("Display order updated");
      refetch();
    },
    onError: (error) => {
      toast.error(`Failed to update order: ${error.message}`);
    },
  });

  useEffect(() => {
    if (allMetrics) {
      setMetrics(allMetrics);
    }
  }, [allMetrics]);

  const handleEdit = (metric: MediaMetric) => {
    setEditingId(metric.id);
    setEditingData({
      account_handle: metric.account_handle,
      display_name: metric.display_name,
      description: metric.description,
      profile_image_url: metric.profile_image_url,
      total_impressions: metric.total_impressions,
      total_views: metric.total_views,
      total_followers: metric.total_followers,
      is_active: metric.is_active,
      display_order: metric.display_order,
    });
  };

  const handleSave = () => {
    if (!editingId || !editingData) return;

    updateMutation.mutate({
      id: editingId,
      ...editingData,
    } as any);
  };

  const handleCancel = () => {
    setEditingId(null);
    setEditingData({});
  };

  const handleAdd = () => {
    if (!editingData.account_handle || !editingData.display_name) {
      toast.error("Account handle and display name are required");
      return;
    }

    const newDisplayOrder = Math.max(...metrics.map(m => m.display_order), 0) + 1;

    createMutation.mutate({
      ...editingData,
      display_order: newDisplayOrder,
      total_impressions: editingData.total_impressions || 0,
      total_views: editingData.total_views || 0,
      total_followers: editingData.total_followers || 0,
      is_active: editingData.is_active ?? true,
    } as any);
  };

  const handleDelete = () => {
    if (!deleteConfirm) return;
    deleteMutation.mutate({ id: deleteConfirm.id });
  };

  const handleToggleActive = (id: string, isActive: boolean) => {
    toggleActiveMutation.mutate({ id, is_active: isActive });
  };

  const handleMoveOrder = (id: string, direction: 'up' | 'down') => {
    const currentMetric = metrics.find(m => m.id === id);
    if (!currentMetric) return;

    const currentOrder = currentMetric.display_order;
    const newOrder = direction === 'up' ? currentOrder - 1 : currentOrder + 1;

    // Find the metric that currently has the target order
    const swapMetric = metrics.find(m => m.display_order === newOrder);
    
    const updates = [
      { id: currentMetric.id, display_order: newOrder }
    ];

    if (swapMetric) {
      updates.push({ id: swapMetric.id, display_order: currentOrder });
    }

    updateOrderMutation.mutate(updates);
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  if (isLoading) {
    return <div className="text-center py-8">Loading metrics...</div>;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold mb-2">Media Account Metrics</h2>
          <p className="text-muted-foreground">
            Manage impression counts and metrics for your media accounts displayed on the landing page.
          </p>
        </div>
        <Button onClick={() => setShowAddForm(true)} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Add Account
        </Button>
      </div>

      {/* Add New Account Form */}
      {showAddForm && (
        <Card className="p-6">
          <h3 className="font-semibold mb-4">Add New Media Account</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="add-handle">Account Handle *</Label>
              <Input
                id="add-handle"
                placeholder="e.g., MarioNawfal"
                value={editingData.account_handle || ''}
                onChange={(e) => setEditingData(prev => ({ ...prev, account_handle: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="add-name">Display Name *</Label>
              <Input
                id="add-name"
                placeholder="e.g., @MarioNawfal"
                value={editingData.display_name || ''}
                onChange={(e) => setEditingData(prev => ({ ...prev, display_name: e.target.value }))}
              />
            </div>
            <div className="md:col-span-2">
              <Label htmlFor="add-description">Description</Label>
              <Input
                id="add-description"
                placeholder="e.g., Daily markets & breaking news"
                value={editingData.description || ''}
                onChange={(e) => setEditingData(prev => ({ ...prev, description: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="add-impressions">Total Impressions</Label>
              <MetricInput
                id="add-impressions"
                label=""
                value={editingData.total_impressions || 0}
                onChange={(val) => setEditingData(prev => ({ ...prev, total_impressions: val }))}
                placeholder="e.g., 2.5m"
              />
            </div>
            <div>
              <Label htmlFor="add-views">Total Views</Label>
              <MetricInput
                id="add-views"
                label=""
                value={editingData.total_views || 0}
                onChange={(val) => setEditingData(prev => ({ ...prev, total_views: val }))}
                placeholder="e.g., 500k"
              />
            </div>
          </div>
          <div className="flex gap-2 mt-4">
            <Button onClick={handleAdd} disabled={createMutation.isPending}>
              {createMutation.isPending ? "Creating..." : "Create Account"}
            </Button>
            <Button variant="outline" onClick={() => { setShowAddForm(false); setEditingData({}); }}>
              Cancel
            </Button>
          </div>
        </Card>
      )}

      {/* Metrics Table */}
      <div className="space-y-4">
        {metrics.map((metric) => (
          <Card key={metric.id} className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="flex flex-col items-center gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleMoveOrder(metric.id, 'up')}
                    disabled={metric.display_order === 1}
                  >
                    <ArrowUp className="h-3 w-3" />
                  </Button>
                  <span className="text-xs text-muted-foreground">{metric.display_order}</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleMoveOrder(metric.id, 'down')}
                    disabled={metric.display_order === Math.max(...metrics.map(m => m.display_order))}
                  >
                    <ArrowDown className="h-3 w-3" />
                  </Button>
                </div>
                
                <div>
                  <div className="flex items-center gap-2">
                    {editingId === metric.id ? (
                      <Input
                        value={editingData.display_name || ''}
                        onChange={(e) => setEditingData(prev => ({ ...prev, display_name: e.target.value }))}
                        className="w-48"
                      />
                    ) : (
                      <h3 className="font-semibold">{metric.display_name}</h3>
                    )}
                    
                    <Badge variant={metric.is_active ? "default" : "secondary"}>
                      {metric.is_active ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                  
                  {editingId === metric.id ? (
                    <Input
                      value={editingData.description || ''}
                      onChange={(e) => setEditingData(prev => ({ ...prev, description: e.target.value }))}
                      className="w-64 mt-1"
                      placeholder="Description"
                    />
                  ) : (
                    <p className="text-sm text-muted-foreground">{metric.description}</p>
                  )}
                  
                  <p className="text-xs text-muted-foreground">Handle: {metric.account_handle}</p>
                </div>
              </div>

              <div className="flex items-center gap-6">
                <div className="flex gap-4 text-sm">
                  <div className="flex items-center gap-1">
                    <BarChart3 className="h-4 w-4 text-blue-500" />
                    <span className="text-muted-foreground">Impressions:</span>
                    {editingId === metric.id ? (
                      <div className="w-32">
                        <MetricInput
                          id={`impressions-${metric.id}`}
                          label=""
                          value={editingData.total_impressions || 0}
                          onChange={(val) => setEditingData(prev => ({ ...prev, total_impressions: val }))}
                          placeholder="e.g., 2.5m"
                        />
                      </div>
                    ) : (
                      <span className="font-medium">{formatNumber(metric.total_impressions)}</span>
                    )}
                  </div>

                  <div className="flex items-center gap-1">
                    <Eye className="h-4 w-4 text-green-500" />
                    <span className="text-muted-foreground">Views:</span>
                    {editingId === metric.id ? (
                      <div className="w-32">
                        <MetricInput
                          id={`views-${metric.id}`}
                          label=""
                          value={editingData.total_views || 0}
                          onChange={(val) => setEditingData(prev => ({ ...prev, total_views: val }))}
                          placeholder="e.g., 500k"
                        />
                      </div>
                    ) : (
                      <span className="font-medium">{formatNumber(metric.total_views)}</span>
                    )}
                  </div>

                  <div className="flex items-center gap-1">
                    <Users className="h-4 w-4 text-purple-500" />
                    <span className="text-muted-foreground">Followers:</span>
                    {editingId === metric.id ? (
                      <div className="w-32">
                        <MetricInput
                          id={`followers-${metric.id}`}
                          label=""
                          value={editingData.total_followers || 0}
                          onChange={(val) => setEditingData(prev => ({ ...prev, total_followers: val }))}
                          placeholder="e.g., 1.2m"
                        />
                      </div>
                    ) : (
                      <span className="font-medium">{formatNumber(metric.total_followers)}</span>
                    )}
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <Switch
                    checked={metric.is_active}
                    onCheckedChange={(checked) => handleToggleActive(metric.id, checked)}
                  />
                  
                  {editingId === metric.id ? (
                    <div className="flex gap-1">
                      <Button size="sm" onClick={handleSave} disabled={updateMutation.isPending}>
                        <Save className="h-3 w-3" />
                      </Button>
                      <Button size="sm" variant="outline" onClick={handleCancel}>
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  ) : (
                    <div className="flex gap-1">
                      <Button size="sm" variant="outline" onClick={() => handleEdit(metric)}>
                        <Edit3 className="h-3 w-3" />
                      </Button>
                      <Button 
                        size="sm" 
                        variant="outline" 
                        onClick={() => setDeleteConfirm({ id: metric.id, name: metric.display_name })}
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* Delete Confirmation Dialog */}
      {deleteConfirm && (
        <ConfirmDialog
          open={!!deleteConfirm}
          onCancel={() => setDeleteConfirm(null)}
          title="Delete Media Account"
          description={`Are you sure you want to delete "${deleteConfirm.name}"? This action cannot be undone.`}
          onConfirm={handleDelete}
          confirmLabel="Delete"
          loading={deleteMutation.isPending}
        />
      )}
    </div>
  );
}