import { useState } from "react";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { useMetadataManagement } from "@/hooks/useMetadataManagement";
import {
  Plus, Trash2, Edit2, X, Check, Merge,
  Hash, BarChart3,
  Download, Upload, Search, RefreshCw,
  Copy, AlertCircle, TrendingUp
} from "lucide-react";
import { toast } from "sonner";
import { ConfirmDialog } from "@/components/ui/confirm-dialog";
import { trpc } from "@/utils/trpc";


interface MetadataManagementProps {
  activeTab: 'content' | 'metadata';
  showForm: boolean;
}

type TabType = 'enums' | 'metrics';

// Type for filter metadata operations - matches the tRPC schema
type FilterMetadataType = 'content_tags' | 'content_account' | 'content_types' | 'twitter_content_type' | 'content_categories';

export function MetadataManagement({ activeTab, showForm }: MetadataManagementProps) {
  const {
    metadataQuery,
    addContentTypeMutation,
    addContentCategoryMutation,
    deleteContentTypeMutation,
    deleteContentCategoryMutation,
    addTwitterContentTypeMutation,
    deleteTwitterContentTypeMutation,
    renameFilterMutation,
    mergeFilterMutation,
  } = useMetadataManagement(activeTab, showForm);

  const [currentTab, setCurrentTab] = useState<TabType>('enums');
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [editingItem, setEditingItem] = useState<{type: FilterMetadataType, oldValue: string, newValue: string} | null>(null);
  const [mergeMode, setMergeMode] = useState(false);
  const [showConfirm, setShowConfirm] = useState<{open: boolean; title: string; description?: string; onConfirm: () => void} | null>(null);

  const [mergeTarget, setMergeTarget] = useState<string | null>(null);

  // Input states for different metadata types
  const [newContentType, setNewContentType] = useState("");
  const [newContentCategory, setNewContentCategory] = useState("");
  const [newTwitterContentType, setNewTwitterContentType] = useState("");

  if (metadataQuery.isLoading) {
    return (
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold">Metadata Management</h2>
          <RefreshCw className="w-4 h-4 animate-spin" />
        </div>
        <p className="text-muted-foreground">Loading metadata...</p>
      </Card>
    );
  }

  const metadata = metadataQuery.data;

  // Tab navigation
  const tabs = [
    { id: 'enums' as TabType, label: 'Enums', icon: Hash },
    { id: 'metrics' as TabType, label: 'Metrics', icon: BarChart3 },
  ];

  // Handle inline editing
  const handleInlineEdit = (type: FilterMetadataType, oldValue: string) => {
    setEditingItem({ type, oldValue, newValue: oldValue });
  };

  const saveInlineEdit = () => {
    if (editingItem && editingItem.newValue !== editingItem.oldValue) {
      renameFilterMutation.mutate({
        type: editingItem.type,
        oldValue: editingItem.oldValue,
        newValue: editingItem.newValue,
      });
    }
    setEditingItem(null);
  };

  // Get current merge type based on active tab and context
  const getCurrentMergeType = (): FilterMetadataType => {
    switch (currentTab) {
      case 'enums':
        return 'content_types';
      case 'metrics':
        return 'content_tags';
      default:
        return 'content_types';
    }
  };

  // Handle merge operations
  const handleMerge = () => {
    if (mergeTarget && selectedItems.size > 0) {
      const sourceValues = Array.from(selectedItems).filter(v => v !== mergeTarget);
      if (sourceValues.length > 0) {
        mergeFilterMutation.mutate({
          type: getCurrentMergeType(),
          sourceValues,
          targetValue: mergeTarget,
        });
        setSelectedItems(new Set());
        setMergeMode(false);
        setMergeTarget(null);
      }
    }
  };

  // Handle bulk selection
  const toggleSelection = (value: string) => {
    const newSelection = new Set(selectedItems);
    if (newSelection.has(value)) {
      newSelection.delete(value);
    } else {
      newSelection.add(value);
    }
    setSelectedItems(newSelection);
  };

  const clearSelection = () => {
    setSelectedItems(new Set());
    setMergeMode(false);
    setMergeTarget(null);
  };

  // Filter data based on search
  const filterData = (data: any[] | undefined) => {
    if (!data) return [];
    if (!searchQuery) return data;
    return data.filter(item =>
      item.value?.toLowerCase().includes(searchQuery.toLowerCase())
    );
  };


  return (
    <div className="space-y-6">
      {/* Header with Actions */}
      <Card className="p-4">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold">Metadata Management</h2>
          <div className="flex items-center gap-2">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search metadata..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-8 w-64"
              />
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => metadataQuery.refetch()}
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </Button>
            <Button variant="outline" size="sm">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
            <Button variant="outline" size="sm">
              <Upload className="w-4 h-4 mr-2" />
              Import
            </Button>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="flex gap-2 border-b">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setCurrentTab(tab.id)}
                className={`flex items-center gap-2 px-4 py-2 border-b-2 transition-colors ${
                  currentTab === tab.id
                    ? 'border-primary text-primary'
                    : 'border-transparent text-muted-foreground hover:text-foreground'
                }`}
              >
                <Icon className="w-4 h-4" />
                {tab.label}
              </button>
            );
          })}
        </div>
      </Card>

      {/* Selection Actions Bar */}
      {selectedItems.size > 0 && (
        <Card className="p-3 bg-muted/50">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Badge variant="secondary">
                {selectedItems.size} selected
              </Badge>
              <Button
                size="sm"
                variant="outline"
                onClick={clearSelection}
              >
                Clear
              </Button>
            </div>
            <div className="flex items-center gap-2">
              {mergeMode ? (
                <>
                  <span className="text-sm text-muted-foreground">
                    Select target to merge into:
                  </span>
                  <Button
                    size="sm"
                    variant="default"
                    onClick={handleMerge}
                    disabled={!mergeTarget}
                  >
                    <Merge className="w-4 h-4 mr-2" />
                    Merge
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setMergeMode(false)}
                  >
                    Cancel
                  </Button>
                </>
              ) : (
                <>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setMergeMode(true)}
                  >
                    <Merge className="w-4 h-4 mr-2" />
                    Merge Selected
                  </Button>
                  <Button
                    size="sm"
                    variant="destructive"
                    onClick={() => {
                      console.log("[Metadata] Bulk delete clicked", { count: selectedItems.size, currentTab });
                      setShowConfirm({
                        open: true,
                        title: `Delete ${selectedItems.size} items?`,
                        description: "This will remove the selected items. This cannot be undone.",
                        onConfirm: () => {
                          const currentType = getCurrentMergeType();
                          console.log("[Metadata] Proceeding with bulk delete", { currentType, selected: Array.from(selectedItems) });
                          toast.info(`Bulk delete for ${currentType} would be implemented here`);
                          setSelectedItems(new Set());
                        }
                      });
                    }}
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    Delete Selected
                  </Button>
                </>
              )}
            </div>
          </div>
        </Card>
      )}

      {/* Tab Content */}
      <Card className="p-6">
        {/* Enums Tab */}
        {currentTab === 'enums' && (
          <div className="space-y-8">
            {/* Content Types */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium flex items-center gap-2">
                  <Hash className="w-4 h-4" />
                  Content Types
                </h3>
                <Badge variant="outline">{metadata?.content_types?.length || 0} total</Badge>
              </div>
              <div className="flex gap-2 mb-4">
                <Input
                  placeholder="Add new content type..."
                  value={newContentType}
                  onChange={(e) => setNewContentType(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                    }
                  }}
                />
                <Button
                  onClick={() => {
                    if (newContentType.trim()) {
                      addContentTypeMutation.mutate({ value: newContentType.trim() });
                      setNewContentType("");
                    }
                  }}
                  disabled={!newContentType.trim() || addContentTypeMutation.isPending}
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add
                </Button>
              </div>
              <div className="flex flex-wrap gap-2">
                {filterData(metadata?.content_types)?.map((type) => (
                  <div key={type.value} className="group relative">
                    {editingItem?.type === 'content_types' && editingItem?.oldValue === type.value ? (
                      <div className="flex items-center gap-1">
                        <Input
                          value={editingItem.newValue}
                          onChange={(e) => setEditingItem({...editingItem, newValue: e.target.value})}
                          className="h-7 w-32"
                          autoFocus
                          onBlur={saveInlineEdit}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') saveInlineEdit();
                            if (e.key === 'Escape') setEditingItem(null);
                          }}
                        />
                        <Button size="sm" variant="ghost" onClick={saveInlineEdit}>
                          <Check className="w-3 h-3" />
                        </Button>
                        <Button size="sm" variant="ghost" onClick={() => setEditingItem(null)}>
                          <X className="w-3 h-3" />
                        </Button>
                      </div>
                    ) : (
                      <Badge
                        variant={mergeMode && mergeTarget === type.value ? "default" : "secondary"}
                        className="text-sm px-3 py-1 cursor-pointer hover:bg-secondary/80"
                        onClick={() => {
                          if (mergeMode) {
                            setMergeTarget(type.value);
                          } else {
                            toggleSelection(type.value);
                          }
                        }}
                      >
                        {selectedItems.has(type.value) && (
                          <Check className="w-3 h-3 mr-1" />
                        )}
                        {type.value}
                        <span className="ml-1 text-xs text-muted-foreground">({type.usageCount})</span>
                        <div className="ml-2 opacity-60 group-hover:opacity-100 transition-opacity inline-flex gap-1">
                          <Button
                            size="sm"
                            variant="ghost"
                            className="h-6 w-6 p-0 hover:bg-primary/10"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleInlineEdit('content_types', type.value);
                            }}
                          >
                            <Edit2 className="w-3 h-3" />
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            className="h-6 w-6 p-0 hover:bg-red-100 hover:text-red-600"
                            onClick={(e) => {
                              e.stopPropagation();
                              console.log('[Metadata] Delete content type clicked', { value: type.value });
                              setShowConfirm({
                                open: true,
                                title: 'Delete this content type?',
                                description: 'This will remove the enum value if not in use.',
                                onConfirm: () => {
                                  console.log('[Metadata] Confirm delete content type', { value: type.value });
                                  deleteContentTypeMutation.mutate({ value: type.value });
                                }
                              });
                            }}
                            disabled={deleteContentTypeMutation.isPending}
                          >
                            <X className="w-3 h-3" />
                          </Button>
                        </div>
                      </Badge>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Content Categories */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium flex items-center gap-2">
                  <Hash className="w-4 h-4" />
                  Content Categories
                </h3>
                <Badge variant="outline">{metadata?.content_categories?.length || 0} total</Badge>
              </div>
              <div className="flex gap-2 mb-4">
                <Input
                  placeholder="Add new content category..."
                  value={newContentCategory}
                  onChange={(e) => setNewContentCategory(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                    }
                  }}
                />
                <Button
                  onClick={() => {
                    if (newContentCategory.trim()) {
                      addContentCategoryMutation.mutate({ value: newContentCategory.trim() });
                      setNewContentCategory("");
                    }
                  }}
                  disabled={!newContentCategory.trim() || addContentCategoryMutation.isPending}
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add
                </Button>
              </div>
              <div className="flex flex-wrap gap-2">
                {filterData(metadata?.content_categories)?.map((category) => (
                  <div key={category.value} className="group relative">
                    {editingItem?.type === 'content_categories' && editingItem?.oldValue === category.value ? (
                      <div className="flex items-center gap-1">
                        <Input
                          value={editingItem.newValue}
                          onChange={(e) => setEditingItem({...editingItem, newValue: e.target.value})}
                          className="h-7 w-32"
                          autoFocus
                          onBlur={saveInlineEdit}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') saveInlineEdit();
                            if (e.key === 'Escape') setEditingItem(null);
                          }}
                        />
                        <Button size="sm" variant="ghost" onClick={saveInlineEdit}>
                          <Check className="w-3 h-3" />
                        </Button>
                        <Button size="sm" variant="ghost" onClick={() => setEditingItem(null)}>
                          <X className="w-3 h-3" />
                        </Button>
                      </div>
                    ) : (
                      <Badge
                        variant={mergeMode && mergeTarget === category.value ? "default" : "secondary"}
                        className="text-sm px-3 py-1 cursor-pointer hover:bg-secondary/80"
                        onClick={() => {
                          if (mergeMode) {
                            setMergeTarget(category.value);
                          } else {
                            toggleSelection(category.value);
                          }
                        }}
                      >
                        {selectedItems.has(category.value) && (
                          <Check className="w-3 h-3 mr-1" />
                        )}
                        {category.value}
                        <span className="ml-1 text-xs text-muted-foreground">({category.usageCount})</span>
                        <div className="ml-2 opacity-60 group-hover:opacity-100 transition-opacity inline-flex gap-1">
                          <Button
                            size="sm"
                            variant="ghost"
                            className="h-6 w-6 p-0 hover:bg-primary/10"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleInlineEdit('content_categories', category.value);
                            }}
                          >
                            <Edit2 className="w-3 h-3" />
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            className="h-6 w-6 p-0 hover:bg-red-100 hover:text-red-600"
                            onClick={(e) => {
                              e.stopPropagation();
                              console.log('[Metadata] Delete category clicked', { value: category.value });
                              setShowConfirm({
                                open: true,
                                title: 'Delete this category?',
                                description: 'This will remove the enum value if not in use.',
                                onConfirm: () => {
                                  console.log('[Metadata] Confirm delete category', { value: category.value });
                                  deleteContentCategoryMutation.mutate({ value: category.value });
                                }
                              });
                            }}
                            disabled={deleteContentCategoryMutation.isPending}
                          >
                            <X className="w-3 h-3" />
                          </Button>
                        </div>
                      </Badge>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Twitter Content Types */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium flex items-center gap-2">
                  <Hash className="w-4 h-4" />
                  Twitter Content Types
                </h3>
                <Badge variant="outline">{metadata?.twitter_content_type?.length || 0} total</Badge>
              </div>
              <div className="flex gap-2 mb-4">
                <Input
                  placeholder="Add new Twitter content type..."
                  value={newTwitterContentType}
                  onChange={(e) => setNewTwitterContentType(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                    }
                  }}
                />
                <Button
                  onClick={() => {
                    if (newTwitterContentType.trim()) {
                      addTwitterContentTypeMutation.mutate({ value: newTwitterContentType.trim() });
                      setNewTwitterContentType("");
                    }
                  }}
                  disabled={!newTwitterContentType.trim() || addTwitterContentTypeMutation.isPending}
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add
                </Button>
              </div>
              <div className="flex flex-wrap gap-2">
                {filterData(metadata?.twitter_content_type)?.map((type) => (
                  <div key={type.value} className="group relative">
                    <Badge
                      variant="secondary"
                      className="text-sm px-3 py-1 cursor-pointer hover:bg-secondary/80"
                    >
                      {type.value}
                      <span className="ml-1 text-xs text-muted-foreground">({type.usageCount})</span>
                      <div className="ml-2 opacity-60 group-hover:opacity-100 transition-opacity inline-flex gap-1">
                        <Button
                          size="sm"
                          variant="ghost"
                          className="h-6 w-6 p-0 hover:bg-primary/10"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleInlineEdit('twitter_content_type', type.value);
                          }}
                        >
                          <Edit2 className="w-3 h-3" />
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          className="h-6 w-6 p-0 hover:bg-red-100 hover:text-red-600"
                          onClick={(e) => {
                            e.stopPropagation();
                            console.log('[Metadata] Delete twitter content type clicked', { value: type.value });
                            setShowConfirm({
                              open: true,
                              title: 'Delete this Twitter content type?',
                              description: 'This will remove the enum value if not in use.',
                              onConfirm: () => {
                                console.log('[Metadata] Confirm delete twitter content type', { value: type.value });
                                deleteTwitterContentTypeMutation.mutate({ value: type.value });
                              }
                            });
                          }}
                          disabled={deleteTwitterContentTypeMutation.isPending}
                        >
                          <X className="w-3 h-3" />
                        </Button>
                      </div>
                    </Badge>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Metrics Tab */}
        {currentTab === 'metrics' && (
          <div className="space-y-8">
            <div>
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium flex items-center gap-2">
                  <BarChart3 className="w-4 h-4" />
                  Content Metrics Overview
                </h3>
                <Button variant="outline" size="sm">
                  <Download className="w-4 h-4 mr-2" />
                  Export CSV
                </Button>
              </div>

              {/* Metrics Cards (live from Supabase via tRPC) */}
              <MetricsCards />

              {/* Placeholder for metrics - needs backend implementation */}
              <div className="mt-6">
                <h4 className="text-md font-medium mb-3">Content Overview</h4>
                <div className="p-4 bg-muted/50 rounded-lg text-center">
                  <p className="text-muted-foreground">Metrics dashboard will be available once content analytics are implemented in the backend.</p>
                  <Button variant="outline" size="sm" className="mt-2">
                    <BarChart3 className="w-4 h-4 mr-2" />
                    Configure Analytics
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Bulk Operations Tab - Removed */}
        {false && (
          <div className="space-y-8">
            <div>
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium flex items-center gap-2">
                  <Copy className="w-4 h-4" />
                  Bulk Operations
                </h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Import Section */}
                <Card className="p-4">
                  <h4 className="font-medium mb-3 flex items-center gap-2">
                    <Upload className="w-4 h-4" />
                    Import Metadata
                  </h4>
                  <div className="space-y-3">
                    <div>
                      <Label>Import Format</Label>
                      <select className="w-full p-2 border rounded">
                        <option>CSV</option>
                        <option>JSON</option>
                        <option>Excel</option>
                      </select>
                    </div>
                    <div>
                      <Label>File</Label>
                      <Input type="file" accept=".csv,.json,.xlsx" />
                    </div>
                    <Button className="w-full">
                      <Upload className="w-4 h-4 mr-2" />
                      Import
                    </Button>
                  </div>
                </Card>

                {/* Export Section */}
                <Card className="p-4">
                  <h4 className="font-medium mb-3 flex items-center gap-2">
                    <Download className="w-4 h-4" />
                    Export Metadata
                  </h4>
                  <div className="space-y-3">
                    <div>
                      <Label>Export Format</Label>
                      <select className="w-full p-2 border rounded">
                        <option>CSV</option>
                        <option>JSON</option>
                        <option>Excel</option>
                      </select>
                    </div>
                    <div>
                      <Label>Include</Label>
                      <div className="space-y-2">
                        <label className="flex items-center gap-2">
                          <input type="checkbox" defaultChecked />
                          <span>Content Types</span>
                        </label>
                        <label className="flex items-center gap-2">
                          <input type="checkbox" defaultChecked />
                          <span>Categories</span>
                        </label>
                        <label className="flex items-center gap-2">
                          <input type="checkbox" defaultChecked />
                          <span>Tags</span>
                        </label>
                        <label className="flex items-center gap-2">
                          <input type="checkbox" defaultChecked />
                          <span>Accounts</span>
                        </label>
                      </div>
                    </div>
                    <Button className="w-full">
                      <Download className="w-4 h-4 mr-2" />
                      Export
                    </Button>
                  </div>
                </Card>

                {/* Validation Rules */}
                <Card className="p-4">
                  <h4 className="font-medium mb-3 flex items-center gap-2">
                    <AlertCircle className="w-4 h-4" />
                    Validation Rules
                  </h4>
                  <div className="space-y-2">
                    <label className="flex items-center gap-2">
                      <input type="checkbox" defaultChecked />
                      <span>Require content type</span>
                    </label>
                    <label className="flex items-center gap-2">
                      <input type="checkbox" defaultChecked />
                      <span>Require at least one category</span>
                    </label>
                    <label className="flex items-center gap-2">
                      <input type="checkbox" />
                      <span>Require host validation</span>

                    </label>
                    <label className="flex items-center gap-2">
                      <input type="checkbox" />
                      <span>Auto-fix common issues</span>
                    </label>
                  </div>
                  <Button className="w-full mt-3" variant="outline">
                    Save Rules
                  </Button>
                </Card>

                {/* Backup & Restore */}
                <Card className="p-4">
                  <h4 className="font-medium mb-3 flex items-center gap-2">
                    <RefreshCw className="w-4 h-4" />
                    Backup & Restore
                  </h4>
                  <div className="space-y-3">
                    <Button className="w-full" variant="outline">
                      Create Backup
                    </Button>
                    <Button className="w-full" variant="outline">
                      Restore from Backup
                    </Button>
                    <div className="text-sm text-muted-foreground">
                      Last backup: 2 hours ago
                    </div>
                  </div>
                </Card>
              </div>
            </div>
          </div>
        )}
      </Card>

      {/* Summary Statistics */}
      <Card className="p-4 bg-muted/50">
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 text-sm">
          <div className="text-center">
            <span className="font-medium block">Content Types</span>
            <span className="text-2xl font-bold">{metadata?.content_types?.length || 0}</span>
          </div>
          <div className="text-center">
            <span className="font-medium block">Categories</span>
            <span className="text-2xl font-bold">{metadata?.content_categories?.length || 0}</span>
          </div>
          <div className="text-center">
            <span className="font-medium block">Twitter Types</span>
            <span className="text-2xl font-bold">{metadata?.twitter_content_type?.length || 0}</span>
          </div>
          <div className="text-center">
            <span className="font-medium block">Tags</span>
            <span className="text-2xl font-bold">{metadata?.content_tags?.length || 0}</span>
          </div>
          <div className="text-center">
            <span className="font-medium block">Hosts</span>
            <span className="text-2xl font-bold">{metadata?.hosts?.length || 0}</span>
          </div>
          <div className="text-center">
            <span className="font-medium block">Accounts</span>
            <span className="text-2xl font-bold">{metadata?.content_account?.length || 0}</span>
          </div>
        </div>
      </Card>


    <ConfirmDialog
      open={showConfirm?.open ?? false}
      title={showConfirm?.title ?? ''}
      description={showConfirm?.description ?? ''}
      checkboxLabel="I am sure"
      confirmLabel="Confirm"
      onConfirm={() => {
        try {
          showConfirm?.onConfirm();
        } finally {
          setShowConfirm(null);
        }
      }}
      onCancel={() => setShowConfirm(null)}
    />
    </div>
  );
}

function MetricsCards() {
  const { data, isLoading, isError, error } = trpc.stats.getImpactMetricsFromStats.useQuery(undefined, {
    staleTime: 60_000,
  });

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i} className="p-4 animate-pulse h-24" />
        ))}
      </div>
    );
  }

  if (isError) {
    console.error('[MetricsCards] Error fetching metrics:', error);
  }

  const totalImpressions = data?.totalImpressions ?? 0;
  const totalContent = data?.totalContent ?? 0;
  const avgEngagementRate = data?.avgEngagementRate ?? 0;

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <Card className="p-4">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-muted-foreground">Total Impressions</p>
            <p className="text-2xl font-bold">{totalImpressions.toLocaleString()}</p>
          </div>
          <TrendingUp className="w-8 h-8 text-green-500" />
        </div>
      </Card>
      <Card className="p-4">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-muted-foreground">Total Content</p>
            <p className="text-2xl font-bold">{totalContent.toLocaleString()}</p>
          </div>
          <TrendingUp className="w-8 h-8 text-blue-500" />
        </div>
      </Card>
      <Card className="p-4">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-muted-foreground">Avg Engagement</p>
            <p className="text-2xl font-bold">{avgEngagementRate ? `${avgEngagementRate}%` : '—'}</p>
          </div>
          <TrendingUp className="w-8 h-8 text-purple-500" />
        </div>
      </Card>
      <Card className="p-4">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-muted-foreground">Updated</p>
            <p className="text-2xl font-bold">Now</p>
          </div>
          <TrendingUp className="w-8 h-8 text-orange-500" />
        </div>
      </Card>
    </div>
  );
}