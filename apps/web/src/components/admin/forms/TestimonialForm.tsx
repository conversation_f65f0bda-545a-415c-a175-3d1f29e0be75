"use client";

import { useState } from "react";
import { useForm } from "@tanstack/react-form";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card } from "@/components/ui/card";
import { toast } from "sonner";
import { trpc } from "@/utils/trpc";
import { useAdminAuth } from "@/hooks/useAdminAuth";
import { UploadButton, UploadDropzone } from "@/lib/uploadthing/components";
import { X, Upload, Image as ImageIcon, FileUp, CheckCircle } from "lucide-react";

// Validation schema for testimonial form
const testimonialSchema = z.object({
  content_title: z.string().min(1, "Title is required"),
  content_description: z.string().min(1, "Description is required"),
  content_link: z.string().url("Must be a valid URL").optional().or(z.literal("")),
  content_tags: z.array(z.string()).default([]),
  content_account: z.array(z.string()).default([]),
  content_categories: z.array(z.string()).default([]),
  content_views: z.number().default(0),
  content_listeners: z.number().default(0),
  twitter_impressions: z.number().default(0),
  content_follow_increase: z.number().default(0),
  screenshot_urls: z.array(z.string().url()).default([]),
});

type TestimonialFormData = z.infer<typeof testimonialSchema>;

interface TestimonialFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function TestimonialForm({ onSuccess, onCancel }: TestimonialFormProps) {
  const [screenshotUrls, setScreenshotUrls] = useState<string[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});
  const { isAdmin, isLoading: authLoading } = useAdminAuth();
  
  const createTestimonial = trpc.content.admin.create.useMutation({
    onSuccess: () => {
      toast.success("Testimonial created successfully!");
      form.reset();
      setScreenshotUrls([]);
      setIsUploading(false);
      setUploadProgress({});
      onSuccess?.();
    },
    onError: (error) => {
      toast.error(`Failed to create testimonial: ${error.message}`);
    },
  });

  const form = useForm({
    defaultValues: {
      content_title: "",
      content_description: "",
      content_link: "",
      content_tags: [],
      content_account: [''],
      content_categories: [],
      content_views: 0,
      content_listeners: 0,
      twitter_impressions: 0,
      content_follow_increase: 0,
      screenshot_urls: [],
    },
    onSubmit: async ({ value }) => {
      try {
        const validatedData = testimonialSchema.parse({
          ...value,
          screenshot_urls: screenshotUrls,
        });

        // Prepare data for submission - only include content_link if it's not empty
        const submissionData = {
          ...validatedData,
          content_link: validatedData.content_link && validatedData.content_link.trim() !== "" 
            ? validatedData.content_link 
            : undefined,
          content_types: ["testimonials"], // Always set as testimonial
          content_created_date: new Date().toISOString(),
          // Include content_account if provided
          content_account: validatedData.content_account.length > 0 
            ? validatedData.content_account 
            : [],
        };

        await createTestimonial.mutateAsync(submissionData);
      } catch (error) {
        if (error instanceof z.ZodError) {
          toast.error("Please check all required fields");
        } else {
          const errorMessage = error instanceof Error ? error.message : String(error);
          toast.error(`Failed to create testimonial: ${errorMessage}`);
        }
      }
    },
  });

  // Don't render form for non-admins
  if (authLoading) {
    return (
      <Card className="p-6">
        <div className="animate-pulse">Loading admin form...</div>
      </Card>
    );
  }

  if (!isAdmin) {
    return (
      <Card className="p-6">
        <div className="text-center text-muted-foreground">
          <p>Admin access required to create testimonials.</p>
        </div>
      </Card>
    );
  }

  const removeScreenshot = (urlToRemove: string) => {
    setScreenshotUrls(prev => prev.filter(url => url !== urlToRemove));
    // Reset upload state when removing screenshots
    setIsUploading(false);
    setUploadProgress({});
  };

  return (
    <Card className="p-6 max-w-2xl mx-auto">
      <div className="space-y-6">
        <div className="border-b pb-4">
          <h2 className="text-2xl font-semibold">Create New Testimonial</h2>
          <p className="text-muted-foreground mt-1">
            Add a new testimonial with screenshots for the dashboard.
          </p>
        </div>

        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            form.handleSubmit();
          }}
          className="space-y-4"
        >
          {/* Title */}
          <form.Field
            name="content_title"
            children={(field) => (
              <div className="space-y-2">
                <Label htmlFor={field.name}>Testimonial Title *</Label>
                <Input
                  id={field.name}
                  value={field.state.value}
                  onBlur={field.handleBlur}
                  onChange={(e) => field.handleChange(e.target.value)}
                  placeholder="Enter testimonial title..."
                />
                {field.state.meta.errors.length > 0 && (
                  <p className="text-sm text-destructive">
                    {field.state.meta.errors[0]}
                  </p>
                )}
              </div>
            )}
          />

          {/* Description */}
          <form.Field
            name="content_description"
            children={(field) => (
              <div className="space-y-2">
                <Label htmlFor={field.name}>Description *</Label>
                <textarea
                  id={field.name}
                  value={field.state.value}
                  onBlur={field.handleBlur}
                  onChange={(e) => field.handleChange(e.target.value)}
                  placeholder="Describe what this testimonial is about, the impact, or key highlights..."
                  className="min-h-[120px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                />
                <p className="text-xs text-muted-foreground">
                  Provide a clear description of the testimonial content, what it showcases, or its significance.
                </p>
                {field.state.meta.errors.length > 0 && (
                  <p className="text-sm text-destructive">
                    {field.state.meta.errors[0]}
                  </p>
                )}
              </div>
            )}
          />

          {/* Link */}
          <form.Field
            name="content_link"
            children={(field) => (
              <div className="space-y-2">
                <Label htmlFor={field.name}>Testimonial Link</Label>
                <Input
                  id={field.name}
                  type="url"
                  value={field.state.value}
                  onBlur={field.handleBlur}
                  onChange={(e) => field.handleChange(e.target.value)}
                  placeholder="https://... (optional)"
                />
                <p className="text-xs text-muted-foreground">
                  Optional. Add a link to the original testimonial post or content.
                </p>
                {field.state.meta.errors.length > 0 && (
                  <p className="text-sm text-destructive">
                    {field.state.meta.errors[0]}
                  </p>
                )}
              </div>
            )}
          />


          {/* Project Name / Handle */}
          <form.Field
            name="content_account"
            children={(field) => (
              <div className="space-y-2">
                <Label htmlFor={field.name}>Project Name / @Handle (Optional)</Label>
                <Input
                  id={field.name}
                  value={field.state.value.join(', ')}
                  onBlur={field.handleBlur}
                  onChange={(e) => {
                    let value = e.target.value;
                    
                    // Auto-prepend @ if it looks like a handle (starts with alphanumeric and no spaces)
                    if (value && !value.includes(',') && !value.startsWith('@') && 
                        /^[a-zA-Z0-9]/.test(value) && !value.includes(' ')) {
                      value = '@' + value;
                    }
                    
                    const accounts = value.split(',').map(item => item.trim()).filter(Boolean);
                    field.handleChange(accounts);
                  }}
                  placeholder="@username or Project Name (e.g., @MarioNawfal, IBC Group)"
                />
                <p className="text-xs text-muted-foreground">
                  Enter project names or social handles. Use comma-separated values for multiple. '@' symbol is auto-added for handles.
                </p>
                {field.state.meta.errors.length > 0 && (
                  <p className="text-sm text-destructive">
                    {field.state.meta.errors[0]}
                  </p>
                )}
              </div>
            )}
          />

          {/* Screenshots Upload Section */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Screenshots</Label>
              <p className="text-sm text-muted-foreground">
                Upload screenshots of the testimonial (max 5 images, 8MB each)
              </p>
            </div>

            {/* Modern Upload Dropzone */}
            <div className="space-y-4">
              {screenshotUrls.length < 5 && (
                <div className="relative">
                  <UploadDropzone
                    endpoint="testimonialScreenshots"
                    onClientUploadComplete={(res) => {
                      if (res) {
                        console.log("✅ [Testimonial Upload] onClientUploadComplete res:", res);
                        const newUrls = res.map((file) => file?.url || (file as any)?.ufsUrl).filter(Boolean) as string[];
                        console.log("✅ [Testimonial Upload] extracted URLs:", newUrls);

                        let addedCount = 0;
                        setScreenshotUrls((prev) => {
                          const availableSlots = Math.max(0, 5 - prev.length);
                          const urlsToAdd = newUrls.slice(0, availableSlots);
                          addedCount = urlsToAdd.length;
                          console.log("ℹ️ [Testimonial Upload] availableSlots:", availableSlots);
                          console.log("ℹ️ [Testimonial Upload] urlsToAdd:", urlsToAdd);
                          const updated = [...prev, ...urlsToAdd];
                          console.log("✅ [Testimonial Upload] updated screenshotUrls count:", updated.length);
                          return updated;
                        });

                        setIsUploading(false);
                        toast.success(`Added ${addedCount} image${addedCount === 1 ? '' : 's'}.`);
                      }
                    }}
                    onUploadError={(error) => { console.error("❌ [Testimonial Upload] onUploadError:", error);
                      setIsUploading(false);
                      toast.error(`Upload failed: ${error.message}`);
                    }}
                    onUploadBegin={(name) => { console.log("ℹ️ [Testimonial Upload] onUploadBegin:", name);
                      setIsUploading(true);
                      toast.info(`Starting upload: ${name}`);
                    }}

                    className={`
                      border-2 border-dashed rounded-xl p-8 transition-all duration-200 ease-in-out
                      ${isUploading 
                        ? 'border-primary bg-primary/5 border-primary' 
                        : 'border-border hover:border-primary/50 hover:bg-accent/20'
                      }
                      ${screenshotUrls.length > 0 ? 'border-green-200 bg-green-50/50' : ''}
                    `}
                    appearance={{
                      uploadIcon: "text-muted-foreground mb-4",
                      label: "text-lg font-medium text-foreground mb-2",
                      allowedContent: "text-sm text-muted-foreground mb-4",
                      button: `
                        inline-flex items-center justify-center rounded-lg text-sm font-medium 
                        transition-colors focus-visible:outline-none focus-visible:ring-2 
                        focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50
                        bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-6 py-2
                        ${isUploading ? 'opacity-50 cursor-not-allowed' : ''}
                      `,
                    }}
                    content={{
                      uploadIcon: ({ ready, isUploading }) => {
                        if (isUploading) return <FileUp className="h-10 w-10 animate-pulse text-primary" />;
                        if (ready && screenshotUrls.length > 0) return <CheckCircle className="h-10 w-10 text-green-500" />;
                        return <ImageIcon className="h-10 w-10 text-muted-foreground" />;
                      },
                      label: ({ ready, isUploading }) => {
                        if (isUploading) return "Uploading images...";
                        if (ready && screenshotUrls.length === 0) return "Drop screenshots here or click to browse";
                        if (ready && screenshotUrls.length > 0) return `Add more screenshots (${screenshotUrls.length}/5)`;
                        return "Getting ready...";
                      },
                      allowedContent: ({ ready, fileTypes, isUploading }) => {
                        if (isUploading) return "Please wait while we upload your files...";
                        if (!ready) return "Checking what you can upload...";
                        return `Accepted formats: ${fileTypes.join(", ")} • Max 8MB per file`;
                      },
                      button: ({ ready, isUploading }) => {
                        if (isUploading) return <div className="flex items-center gap-2"><Upload className="h-4 w-4 animate-spin" />Uploading...</div>;
                        if (ready) return <div className="flex items-center gap-2"><Upload className="h-4 w-4" />Choose Files</div>;
                        return "Getting ready...";
                      },
                    }}
                  />
                </div>
              )}

              {/* Upload Progress Indicator */}
              {isUploading && (
                <div className="bg-accent/50 rounded-lg p-4 border border-border/50">
                  <div className="flex items-center gap-3">
                    <div className="w-6 h-6 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
                    <div className="flex-1">
                      <p className="text-sm font-medium text-foreground">Uploading screenshots...</p>
                      <p className="text-xs text-muted-foreground">Please don't close this page while uploading</p>
                    </div>
                  </div>
                </div>
              )}

              {/* Enhanced Preview Grid */}
              {screenshotUrls.length > 0 && (
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label className="text-base font-semibold">Uploaded Screenshots</Label>
                    <span className="text-sm text-muted-foreground bg-accent px-2 py-1 rounded-full">
                      {screenshotUrls.length}/5 uploaded
                    </span>
                  </div>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    {screenshotUrls.map((url, index) => (
                      <div key={url} className="relative group">
                        <div className="aspect-video bg-muted rounded-xl overflow-hidden border border-border/50 hover:border-border transition-colors">
                          <img
                            src={url}
                            alt={`Screenshot ${index + 1}`}
                            className="w-full h-full object-cover transition-transform group-hover:scale-105"
                            loading="lazy"
                          />
                        </div>
                        <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity rounded-xl flex items-center justify-center">
                          <button
                            type="button"
                            onClick={() => removeScreenshot(url)}
                            className="bg-destructive/90 text-destructive-foreground rounded-full w-8 h-8 flex items-center justify-center hover:bg-destructive transition-colors"
                            title="Remove screenshot"
                          >
                            <X className="w-4 h-4" />
                          </button>
                        </div>
                        <div className="absolute top-2 left-2 bg-black/70 text-white text-xs px-2 py-1 rounded-full">
                          {index + 1}
                        </div>
                      </div>
                    ))}
                  </div>
                  {screenshotUrls.length === 5 && (
                    <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="w-5 h-5 text-green-600" />
                        <p className="text-sm text-green-800 font-medium">Maximum screenshots uploaded!</p>
                      </div>
                      <p className="text-xs text-green-600 mt-1">You can remove existing screenshots to add new ones.</p>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex gap-3 pt-4">
            <Button
              type="submit"
              disabled={createTestimonial.isPending || isUploading}
              className="flex-1"
            >
              {createTestimonial.isPending 
                ? "Creating..." 
                : isUploading 
                  ? "Upload in progress..." 
                  : "Create Testimonial"
              }
            </Button>
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={createTestimonial.isPending}
              >
                Cancel
              </Button>
            )}
          </div>
        </form>
      </div>
    </Card>
  );
}