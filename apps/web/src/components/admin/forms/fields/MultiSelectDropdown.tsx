import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Plus, ChevronDown, X } from "lucide-react";
import { toast } from "sonner";
import type { MultiSelectDropdownProps } from "@/types/admin";

export const MultiSelectDropdown = ({ 
  label, 
  value, 
  options, 
  onChange, 
  placeholder, 
  onAddNew, 
  addNewLabel, 
  canAddNew = false 
}: MultiSelectDropdownProps) => {
  const [isAddingNew, setIsAddingNew] = useState(false);

  const toggleOption = (option: string) => {
    if (value.includes(option)) {
      onChange(value.filter(item => item !== option));
    } else {
      onChange([...value, option]);
    }
  };

  const removeOption = (option: string) => {
    onChange(value.filter(item => item !== option));
  };

  const handleAddNew = async () => {
    if (!onAddNew) return;
    
    const newValue = prompt(`Add new ${addNewLabel || 'option'}:`);
    if (newValue && newValue.trim()) {
      setIsAddingNew(true);
      try {
        await onAddNew(newValue.trim());
        // Automatically select the newly added option
        if (!value.includes(newValue.trim().toLowerCase())) {
          onChange([...value, newValue.trim().toLowerCase()]);
        }
        toast.success(`${addNewLabel || 'Option'} "${newValue.trim()}" added successfully!`);
      } catch (error) {
        console.error('Error adding new option:', error);
        toast.error(`Failed to add ${addNewLabel || 'option'}`);
      } finally {
        setIsAddingNew(false);
      }
    }
  };

  return (
    <div className="space-y-2">
      <Label>{label}</Label>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button 
            variant="outline" 
            className="w-full justify-between h-auto min-h-10 px-3 py-2 max-h-32"
          >
            <div className="flex flex-wrap gap-1 max-h-24 overflow-y-auto flex-1 pr-2">
              {value.length === 0 ? (
                <span className="text-muted-foreground text-sm">{placeholder || "Select options..."}</span>
              ) : value.length <= 3 ? (
                value.map(item => (
                  <Badge 
                    key={item} 
                    variant="secondary" 
                    className="text-xs cursor-pointer hover:bg-secondary/80"
                    onClick={(e) => {
                      e.stopPropagation();
                      removeOption(item);
                    }}
                  >
                    {item}
                    <X className="ml-1 h-3 w-3" />
                  </Badge>
                ))
              ) : (
                <>
                  {value.slice(0, 2).map(item => (
                    <Badge 
                      key={item} 
                      variant="secondary" 
                      className="text-xs cursor-pointer hover:bg-secondary/80"
                      onClick={(e) => {
                        e.stopPropagation();
                        removeOption(item);
                      }}
                    >
                      {item}
                      <X className="ml-1 h-3 w-3" />
                    </Badge>
                  ))}
                  <Badge variant="outline" className="text-xs">
                    +{value.length - 2} more
                  </Badge>
                </>
              )}
            </div>
            <ChevronDown className="h-4 w-4 opacity-50 flex-shrink-0" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-64 max-h-64 overflow-y-auto">
          <div className="p-2">
            <div className="text-xs text-muted-foreground mb-2">
              {value.length > 0 ? `${value.length} selected` : "Select options"}
            </div>
          </div>
          {canAddNew && onAddNew && (
            <>
              <DropdownMenuItem 
                onClick={handleAddNew}
                className="cursor-pointer px-3 py-2 bg-blue-50 hover:bg-blue-100 dark:bg-blue-950/50 dark:hover:bg-blue-900/50 border-b"
                disabled={isAddingNew}
              >
                <div className="flex items-center space-x-2 w-full">
                  <Plus className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                  <span className="text-blue-600 dark:text-blue-400 font-medium text-sm">
                    {isAddingNew ? 'Adding...' : `Add New ${addNewLabel || 'Option'}`}
                  </span>
                </div>
              </DropdownMenuItem>
              <div className="h-px bg-border my-1" />
            </>
          )}
          {options.map(option => (
            <DropdownMenuItem 
              key={option}
              onClick={() => toggleOption(option)}
              className="cursor-pointer px-3 py-2"
            >
              <div className="flex items-center space-x-2 w-full">
                <div className={`w-4 h-4 border rounded flex-shrink-0 ${value.includes(option) ? 'bg-primary border-primary' : 'border-input'}`}>
                  {value.includes(option) && <div className="w-full h-full bg-primary-foreground rounded-sm m-0.5" />}
                </div>
                <span className="capitalize text-sm flex-1">{option}</span>
                {value.includes(option) && (
                  <X className="h-3 w-3 text-muted-foreground flex-shrink-0" />
                )}
              </div>
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};