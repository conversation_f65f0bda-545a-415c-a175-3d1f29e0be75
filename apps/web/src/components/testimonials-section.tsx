"use client";

import { ScreenshotCarousel } from "@/components/screenshot-carousel";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { 
  ArrowR<PERSON>, 
  Sparkles, 
  Quote,
  TrendingUp
} from "lucide-react";
import type { ContentPiece } from "@/lib/server/supabase";
import Link from "next/link";

interface TestimonialsSectionProps {
  testimonials: ContentPiece[];
  isLoading?: boolean;
  showViewAll?: boolean;
}

export function TestimonialsSection({ 
  testimonials, 
  isLoading = false,
  showViewAll = true 
}: TestimonialsSectionProps) {
  
  // Loading skeleton
  if (isLoading) {
    return (
      <section className="relative py-16 overflow-hidden">
        <div className="relative z-10">
          {/* Header Skeleton */}
          <div className="flex items-center justify-between mb-12">
            <div className="space-y-4">
              <Skeleton className="h-10 w-64" />
              <Skeleton className="h-6 w-96" />
            </div>
            <Skeleton className="h-10 w-32" />
          </div>
          
          {/* Carousel Skeleton */}
          <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="space-y-4">
                <Skeleton className="aspect-video rounded-xl" />
                <div className="space-y-2">
                  <Skeleton className="h-6 w-3/4" />
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-2/3" />
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  // No testimonials state
  if (!testimonials || testimonials.length === 0) {
    return (
      <section className="relative py-16 overflow-hidden">
        <div className="text-center space-y-4">
          <Quote className="w-16 h-16 mx-auto text-muted-foreground/30" />
          <h3 className="text-xl font-semibold text-muted-foreground">
            No testimonials available yet
          </h3>
          <p className="text-muted-foreground/80">
            Check back soon for client success stories
          </p>
        </div>
      </section>
    );
  }

  return (
    <section className="relative w-full min-h-screen overflow-hidden">
      {/* Full-screen Enhanced Background */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Modern gradient backdrop - full screen */}
        <div className="absolute inset-0 bg-gradient-to-br from-background via-muted/5 to-background"></div>
        
        {/* Floating elements - positioned for full screen */}
        <div className="absolute top-1/4 left-1/6 w-96 h-96 rounded-full bg-gradient-to-br from-primary/10 to-transparent blur-3xl animate-pulse" />
        <div className="absolute bottom-1/4 right-1/6 w-96 h-96 rounded-full bg-gradient-to-tl from-accent/8 to-transparent blur-3xl animate-pulse" style={{ animationDelay: '3s' }} />
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-80 h-80 rounded-full bg-gradient-to-r from-primary/5 to-accent/5 blur-3xl animate-pulse" style={{ animationDelay: '1.5s' }} />
        
        {/* Full-width subtle dots pattern */}
        <div 
          className="absolute inset-0 opacity-[0.02] dark:opacity-[0.03]"
          style={{
            backgroundImage: 'radial-gradient(circle at 1px 1px, currentColor 1px, transparent 1px)',
            backgroundSize: '32px 32px'
          }}
        />
        
        {/* Floating icons - positioned for full screen */}
        <div className="absolute top-20 left-20 text-primary/10 animate-bounce" style={{ animationDelay: '1s', animationDuration: '4s' }}>
          <Quote className="w-8 h-8" />
        </div>
        <div className="absolute bottom-20 right-20 text-accent/10 animate-bounce" style={{ animationDelay: '2s', animationDuration: '5s' }}>
          <Sparkles className="w-7 h-7" />
        </div>
        <div className="absolute top-40 right-40 text-primary/8 animate-bounce" style={{ animationDelay: '0.5s', animationDuration: '6s' }}>
          <TrendingUp className="w-6 h-6" />
        </div>
      </div>

      {/* Full-screen Content */}
      <div className="relative z-10 flex flex-col justify-center min-h-screen py-20">
        {/* Section Header - Centered */}
        <div className="text-center mb-20 space-y-8 px-6">
          {/* Modern Title */}
          <div className="space-y-6">
            <h2 className="text-6xl sm:text-7xl lg:text-8xl font-bold bg-gradient-to-r from-foreground via-foreground to-muted-foreground bg-clip-text text-transparent">
              Client Testimonials
            </h2>
            
            {/* Subtitle with better styling */}
            <p className="text-2xl text-muted-foreground max-w-4xl mx-auto leading-relaxed">
              Discover what our clients are saying about their experience with IBC Ventures
            </p>
          </div>

          {/* Decorative line */}
          <div className="flex items-center justify-center gap-6">
            <div className="h-px w-20 bg-gradient-to-r from-transparent to-border"></div>
            <div className="w-3 h-3 rounded-full bg-primary/30"></div>
            <div className="h-px w-20 bg-gradient-to-l from-transparent to-border"></div>
          </div>

          {/* View All Button - Redesigned */}
          {showViewAll && (
            <div className="pt-6">
              <Button
                variant="ghost"
                size="lg"
                className="group gap-3 text-muted-foreground hover:text-foreground transition-all duration-300 text-lg"
                asChild
              >
                <Link href="/testimonials">
                  <span>View All Stories</span>
                  <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
                </Link>
              </Button>
            </div>
          )}
        </div>

        {/* Full-Width Testimonials Carousel */}
        <div className="relative w-full">
          {/* Full-width glow effect behind carousel */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-primary/3 to-transparent blur-2xl -z-10"></div>
          
          <ScreenshotCarousel
            testimonials={testimonials}
            className=""
          />
        </div>

        {/* Bottom CTA - Centered */}
        {testimonials.length > 6 && (
          <div className="mt-20 text-center space-y-8 px-6">
            <div className="space-y-4">
              <p className="text-lg text-muted-foreground">
                Showing {Math.min(testimonials.length, 9)} of {testimonials.length} success stories
              </p>
              <div className="flex items-center justify-center gap-3">
                <div className="h-px w-12 bg-gradient-to-r from-transparent to-border"></div>
                <div className="w-2 h-2 rounded-full bg-primary/50"></div>
                <div className="h-px w-12 bg-gradient-to-l from-transparent to-border"></div>
              </div>
            </div>
            <Button
              variant="outline"
              size="lg"
              className="group gap-3 hover:bg-primary hover:text-primary-foreground transition-all duration-300 text-lg px-8 py-3"
              asChild
            >
              <Link href="/testimonials">
                Explore All Testimonials
                <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
              </Link>
            </Button>
          </div>
        )}
      </div>
    </section>
  );
}