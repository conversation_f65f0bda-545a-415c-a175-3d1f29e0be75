import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ChevronLeft, ChevronRight, X } from "lucide-react"
import { cn } from "@/lib/utils"
import type { ContentPiece } from "@/lib/server/supabase"

interface Review {
  id: number
  name: string
  company: string
  role: string
  content: string
  rating: number
  screenshot: string
  avatar: string
}

interface ScreenshotCarouselProps {
  testimonials: ContentPiece[]
  className?: string
}

// Transform ContentPiece to Review format
const transformContentPieceToReview = (contentPiece: ContentPiece): Review => {
  return {
    id: contentPiece.id,
    name: contentPiece.host || "Anonymous",
    company: contentPiece.content_title || "Client",
    role: contentPiece.content_account?.[0] || "Team Member", 
    content: contentPiece.content_description || "Amazing results delivered with exceptional quality and attention to detail.",
    rating: 5,
    screenshot: contentPiece.screenshot_urls?.[0] || "/placeholder.svg",
    avatar: "/professional-avatar.png",
  }
}

export function ScreenshotCarousel({ testimonials, className }: ScreenshotCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(true)
  const [fullscreenImage, setFullscreenImage] = useState<string | null>(null)

  // Transform ContentPiece data to Review format
  const reviews = testimonials.map(transformContentPieceToReview)

  useEffect(() => {
    if (!isAutoPlaying || reviews.length === 0) return

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % reviews.length)
    }, 5000)

    return () => clearInterval(interval)
  }, [isAutoPlaying, reviews.length])

  const goToPrevious = () => {
    setCurrentIndex((prev) => (prev - 1 + reviews.length) % reviews.length)
    setIsAutoPlaying(false)
  }

  const goToNext = () => {
    setCurrentIndex((prev) => (prev + 1) % reviews.length)
    setIsAutoPlaying(false)
  }

  const goToSlide = (index: number) => {
    setCurrentIndex(index)
    setIsAutoPlaying(false)
  }

  const openFullscreen = (screenshot: string) => {
    setFullscreenImage(screenshot)
  }

  const closeFullscreen = () => {
    setFullscreenImage(null)
  }

  const getVisibleReviews = () => {
    if (reviews.length === 0) return []
    
    const visible = []
    const visibleCount = Math.min(3, reviews.length) // Don't show more cards than we have reviews
    
    for (let i = 0; i < visibleCount; i++) {
      const index = (currentIndex + i) % reviews.length
      visible.push({ 
        ...reviews[index], 
        displayIndex: i,
        // Create unique key to avoid duplicates when we have fewer than 3 reviews
        uniqueKey: `${reviews[index].id}-${currentIndex}-${i}`
      })
    }
    return visible
  }

  const visibleReviews = getVisibleReviews()

  if (!reviews || reviews.length === 0) {
    return null
  }

  return (
    <section className={cn("w-full max-w-6xl mx-auto", className)}>
      <div className="relative">
        <div className="flex gap-6 justify-center">
          {visibleReviews.map((review, index) => (
            <Card
              key={review.uniqueKey}
              className={cn(
                "overflow-hidden border-0 shadow-2xl bg-card w-80 h-[28rem] transition-all duration-500 flex flex-col",
                index === 0 ? "scale-105 z-10" : "scale-95 opacity-75",
              )}
            >
              <CardContent className="p-0 flex flex-col h-full">
                {/* Screenshot Section - 80% of card height */}
                <div className="relative overflow-hidden flex-grow">
                  <div
                    className="h-full relative group cursor-pointer"
                    onClick={() => openFullscreen(review.screenshot)}
                  >
                    <img
                      src={review.screenshot || "/placeholder.svg"}
                      alt={`${review.company} project screenshot`}
                      className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-105"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <div className="bg-white/90 dark:bg-black/90 rounded-full p-3">
                        <svg
                          className="w-6 h-6 text-violet-600"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"
                          />
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Content Section - 20% of card height */}
                <div className="h-28 p-4 flex flex-col justify-center bg-card">
                  <div className="space-y-2">
                    <h3 className="font-semibold text-foreground font-sans text-lg leading-tight line-clamp-1">
                      {review.company}
                    </h3>
                    <p className="text-sm text-muted-foreground font-sans line-clamp-2">{review.content}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Navigation Arrows */}
        <Button
          variant="outline"
          size="icon"
          className="absolute left-4 top-1/2 -translate-y-1/2 w-12 h-12 rounded-full bg-background/80 backdrop-blur-sm border-2 hover:bg-violet-600 hover:text-white hover:border-violet-600 transition-all duration-300 shadow-lg"
          onClick={goToPrevious}
        >
          <ChevronLeft className="w-5 h-5" />
        </Button>

        <Button
          variant="outline"
          size="icon"
          className="absolute right-4 top-1/2 -translate-y-1/2 w-12 h-12 rounded-full bg-background/80 backdrop-blur-sm border-2 hover:bg-violet-600 hover:text-white hover:border-violet-600 transition-all duration-300 shadow-lg"
          onClick={goToNext}
        >
          <ChevronRight className="w-5 h-5" />
        </Button>
      </div>

      {/* Dots Indicator */}
      <div className="flex justify-center gap-3 mt-8">
        {reviews.map((_, index) => (
          <button
            key={index}
            className={cn(
              "w-3 h-3 rounded-full transition-all duration-300",
              index === currentIndex
                ? "bg-violet-600 scale-125"
                : "bg-muted-foreground/30 hover:bg-muted-foreground/50",
            )}
            onClick={() => goToSlide(index)}
          />
        ))}
      </div>


      {fullscreenImage && (
        <div className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4" onClick={closeFullscreen}>
          <div className="relative max-w-7xl max-h-full">
            <img
              src={fullscreenImage || "/placeholder.svg"}
              alt="Fullscreen screenshot"
              className="max-w-full max-h-full object-contain"
            />
            <Button
              variant="outline"
              size="icon"
              className="absolute top-4 right-4 bg-white/10 backdrop-blur-sm border-white/20 text-white hover:bg-white/20"
              onClick={closeFullscreen}
            >
              <X className="w-5 h-5" />
            </Button>
          </div>
        </div>
      )}
    </section>
  )
}
