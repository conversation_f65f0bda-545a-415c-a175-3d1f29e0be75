"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { trpc } from "@/utils/trpc";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "sonner";
import { Eye, EyeOff, Mail, Lock, Chrome } from "lucide-react";

interface LoginFormProps {
  onClose?: () => void;
}

export function LoginForm({ onClose }: LoginFormProps) {
  const [isSignUp, setIsSignUp] = useState(false);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  
  const router = useRouter();
  
  const signUpMutation = trpc.auth.signUp.useMutation({
    onSuccess: () => {
      toast.success("Check your email for the confirmation link!");
      onClose?.();
    },
    onError: (error) => {
      toast.error(error.message);
    },
    onSettled: () => {
      setIsLoading(false);
    },
  });
  
  const signInMutation = trpc.auth.signIn.useMutation({
    onSuccess: () => {
      toast.success("Successfully signed in!");
      router.refresh();
      onClose?.();
    },
    onError: (error) => {
      toast.error(error.message);
    },
    onSettled: () => {
      setIsLoading(false);
    },
  });

  const googleSignInMutation = trpc.auth.signInWithGoogle.useMutation({
    onSuccess: (data) => {
      if (data.url) {
        window.location.href = data.url;
      }
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email || !password) {
      toast.error("Please fill in all fields");
      return;
    }
    
    setIsLoading(true);
    
    if (isSignUp) {
      signUpMutation.mutate({ email, password });
    } else {
      signInMutation.mutate({ email, password });
    }
  };

  const handleGoogleSignIn = () => {
    googleSignInMutation.mutate();
  };
  
  return (
    <Card className="w-full bg-background/98 backdrop-blur-md border-2 shadow-2xl">
      <CardHeader className="space-y-4 pb-8">
        <CardTitle className="text-3xl font-bold text-center text-foreground">
          {isSignUp ? "Create Account" : "Welcome Back"}
        </CardTitle>
        <CardDescription className="text-center text-muted-foreground text-base">
          {isSignUp 
            ? "Enter your details to create your account" 
            : "Enter your credentials to access your account"
          }
        </CardDescription>
      </CardHeader>
      <CardContent className="px-8 pb-8">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-3">
            <Label htmlFor="email" className="text-sm font-semibold text-foreground">
              Email Address
            </Label>
            <div className="relative">
              <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-5 w-5" />
              <Input
                id="email"
                type="email"
                placeholder="Enter your email address"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="pl-11 pr-4 h-12 bg-background/50 border-2 border-border focus:border-primary focus:ring-2 focus:ring-primary/20 rounded-lg text-base transition-all"
                required
              />
            </div>
          </div>
          
          <div className="space-y-3">
            <Label htmlFor="password" className="text-sm font-semibold text-foreground">
              Password
            </Label>
            <div className="relative">
              <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-5 w-5" />
              <Input
                id="password"
                type={showPassword ? "text" : "password"}
                placeholder={isSignUp ? "Create a secure password (min 6 chars)" : "Enter your password"}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="pl-11 pr-12 h-12 bg-background/50 border-2 border-border focus:border-primary focus:ring-2 focus:ring-primary/20 rounded-lg text-base transition-all"
                minLength={isSignUp ? 6 : undefined}
                required
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors p-1"
              >
                {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
              </button>
            </div>
          </div>
          
          <Button 
            type="submit" 
            className="w-full h-12 text-base font-semibold bg-primary hover:bg-primary/90 transition-colors rounded-lg" 
            disabled={isLoading}
          >
            {isLoading ? "Processing..." : (isSignUp ? "Create Account" : "Sign In")}
          </Button>
        </form>
        
        <div className="mt-6">
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t border-border/50" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-3 text-muted-foreground font-medium">Or continue with</span>
            </div>
          </div>
          
          <Button
            type="button"
            variant="outline"
            onClick={handleGoogleSignIn}
            disabled={googleSignInMutation.isPending}
            className="w-full mt-4 h-12 text-base font-medium border-2 hover:bg-muted/50 transition-colors rounded-lg"
          >
            <Chrome className="mr-3 h-5 w-5" />
            {googleSignInMutation.isPending ? "Connecting..." : "Continue with Google"}
          </Button>
        </div>
        
        <div className="mt-6 pt-4 border-t border-border/50">
          <div className="text-center">
            <button
              type="button"
              onClick={() => setIsSignUp(!isSignUp)}
              className="text-sm text-muted-foreground hover:text-primary font-medium transition-colors"
            >
              {isSignUp 
                ? "Already have an account? Sign in instead" 
                : "Don't have an account? Create one"
              }
            </button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}