"use client";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { trpc } from "@/utils/trpc";
import { AuthButton } from "@/components/auth/auth-button";
import { But<PERSON> } from "@/components/ui/button";
import { IBCL<PERSON> } from "@/components/ui/ibc-logo";
import { BarChart3, Zap, Shield, Folder } from "lucide-react";

export default function Header() {
  const pathname = usePathname();
  const { data: user, isLoading: userLoading } = trpc.auth.getCurrentUser.useQuery(undefined, {
    retry: 1,
    staleTime: 5 * 60 * 1000,
    refetchOnWindowFocus: false,
  });

  // Get user profile to check if admin (only if user is authenticated)
  const userProfileQuery = trpc.auth.getUserProfile.useQuery(undefined, {
    enabled: !!user && !userLoading,
    retry: false,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });

  const isAdmin = userProfileQuery.data?.role === 'admin';

  // Determine which page we're on
  const isLandingPage = pathname === '/';
  const isDashboardPage = pathname === '/dashboard';
  const isGroupsPage = pathname?.startsWith('/groups');

  console.log('Header: Current pathname:', pathname, 'isLandingPage:', isLandingPage, 'isDashboardPage:', isDashboardPage);
  
  return (
    <header className="sticky top-0 z-50 w-full glass-effect border-b border-border/30">
      <div className="content-container">
        <div className="center-all justify-between h-20">
          {/* Brand */}
          <Link href="/" className="hover:opacity-90 transition-opacity duration-300 flex items-center">
            <IBCLogo className="text-ibc-green" width={100} height={45} />
          </Link>
          
          {/* Navigation */}
          <div className="flex items-center gap-4">
            {/* Conditional Navigation based on current page */}
            {isLandingPage && (
              /* Show Dashboard button on landing page */
              <Link href="/dashboard">
                <Button variant="default" className="flex items-center space-x-2">
                  <BarChart3 className="h-4 w-4" />
                  <span>Dashboard</span>
                </Button>
              </Link>
            )}

            {isDashboardPage && (
              /* Show Groups and Auth buttons on dashboard page */
              <>
                {user && (
                  <Link href="/groups">
                    <Button variant="outline" size="sm" className="flex items-center gap-2">
                      <Folder className="h-4 w-4" />
                      Groups
                    </Button>
                  </Link>
                )}
                <AuthButton user={user} />
              </>
            )}

            {isGroupsPage && (
              /* Show Dashboard and Auth buttons on groups pages */
              <>
                <Link href="/dashboard">
                  <Button variant="outline" size="sm" className="flex items-center gap-2">
                    <BarChart3 className="h-4 w-4" />
                    Dashboard
                  </Button>
                </Link>
                <AuthButton user={user} />
              </>
            )}

            {/* Admin Link - only show for admin users */}
            {isAdmin && (
              <Link href="/admin">
                <Button variant="outline" size="sm" className="flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  Admin
                </Button>
              </Link>
            )}

            {/* Show auth button on other pages (not landing, dashboard, or groups) */}
            {!isLandingPage && !isDashboardPage && !isGroupsPage && (
              <AuthButton user={user} />
            )}
          </div>
        </div>
      </div>
    </header>
  );
}
