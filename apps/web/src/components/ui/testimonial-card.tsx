import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import type { ContentPiece } from "@/lib/server/supabase";
import { ExternalLink, Image as ImageIcon, Eye, EyeOff, Calendar, User } from "lucide-react";
import Image from "next/image";

interface TestimonialCardProps {
  testimonial: ContentPiece;
  className?: string;
}

export function TestimonialCard({ testimonial, className }: TestimonialCardProps) {
  const [showImages, setShowImages] = useState(true);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  
  const hasScreenshots = testimonial.screenshot_urls && testimonial.screenshot_urls.length > 0;
  
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  return (
    <Card className={`overflow-hidden hover:shadow-lg transition-all duration-300 ${className}`}>
      {/* Image Gallery Section */}
      {hasScreenshots && showImages && (
        <div className="relative">
          <div className="aspect-video bg-muted overflow-hidden">
            <Image
              src={testimonial.screenshot_urls![selectedImageIndex]}
              alt={`${testimonial.content_title || 'Testimonial'} screenshot ${selectedImageIndex + 1}`}
              fill
              className="object-cover transition-opacity duration-200"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
          </div>
          
          {/* Image Navigation */}
          {testimonial.screenshot_urls!.length > 1 && (
            <div className="absolute bottom-2 left-1/2 -translate-x-1/2 flex gap-1">
              {testimonial.screenshot_urls!.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedImageIndex(index)}
                  className={`w-2 h-2 rounded-full transition-colors ${
                    index === selectedImageIndex 
                      ? 'bg-white' 
                      : 'bg-white/50 hover:bg-white/75'
                  }`}
                  aria-label={`View screenshot ${index + 1}`}
                />
              ))}
            </div>
          )}

          {/* Image Counter */}
          <div className="absolute top-2 right-2 bg-black/50 text-white text-xs px-2 py-1 rounded-full">
            {selectedImageIndex + 1} / {testimonial.screenshot_urls!.length}
          </div>

          {/* Toggle Images Button */}
          <Button
            size="sm"
            variant="outline"
            className="absolute top-2 left-2 bg-white/90 hover:bg-white"
            onClick={() => setShowImages(false)}
          >
            <EyeOff className="w-4 h-4" />
          </Button>
        </div>
      )}

      {/* Show Images Button (when images are hidden) */}
      {hasScreenshots && !showImages && (
        <div className="p-4 border-b border-border">
          <Button
            size="sm"
            variant="outline"
            onClick={() => setShowImages(true)}
            className="w-full"
          >
            <Eye className="w-4 h-4 mr-2" />
            Show Screenshots ({testimonial.screenshot_urls!.length})
          </Button>
        </div>
      )}

      {/* Content Section */}
      <div className="p-6 space-y-4">
        {/* Header */}
        <div className="space-y-3">
          <div className="flex items-start justify-between gap-2">
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-lg leading-tight line-clamp-2">
                {testimonial.content_title || "Untitled Testimonial"}
              </h3>
              <div className="flex items-center gap-2 mt-2 text-sm text-muted-foreground">
                <User className="w-4 h-4" />
                <span>{testimonial.host}</span>
                <span>•</span>
                <Calendar className="w-4 h-4" />
                <span>{formatDate(testimonial.content_created_date)}</span>
              </div>
            </div>
            
            <Button
              size="sm"
              variant="outline"
              asChild
            >
              <a 
                href={testimonial.content_link} 
                target="_blank" 
                rel="noopener noreferrer"
                className="flex items-center gap-1"
              >
                <ExternalLink className="w-4 h-4" />
                <span className="sr-only">View original</span>
              </a>
            </Button>
          </div>

          {/* Description */}
          {testimonial.content_description && (
            <p className="text-muted-foreground text-sm line-clamp-3">
              {testimonial.content_description}
            </p>
          )}
        </div>

        {/* Tags */}
        {testimonial.content_tags && testimonial.content_tags.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {testimonial.content_tags.slice(0, 3).map((tag) => (
              <Badge key={tag} variant="secondary" className="text-xs">
                {tag}
              </Badge>
            ))}
            {testimonial.content_tags.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{testimonial.content_tags.length - 3} more
              </Badge>
            )}
          </div>
        )}

        {/* Categories */}
        {testimonial.content_categories && testimonial.content_categories.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {testimonial.content_categories.map((category) => (
              <Badge key={category} variant="outline" className="text-xs capitalize">
                {category}
              </Badge>
            ))}
          </div>
        )}

        {/* Metrics */}
        <div className="flex flex-wrap gap-4 text-sm text-muted-foreground pt-2 border-t border-border">
          {testimonial.content_views > 0 && (
            <div className="flex items-center gap-1">
              <Eye className="w-4 h-4" />
              <span>{formatNumber(testimonial.content_views)} views</span>
            </div>
          )}
          {testimonial.twitter_impressions > 0 && (
            <div className="flex items-center gap-1">
              <ImageIcon className="w-4 h-4" />
              <span>{formatNumber(testimonial.twitter_impressions)} impressions</span>
            </div>
          )}
          {!hasScreenshots && (
            <div className="flex items-center gap-1 text-muted-foreground/60">
              <ImageIcon className="w-4 h-4" />
              <span>No screenshots</span>
            </div>
          )}
        </div>
      </div>
    </Card>
  );
}