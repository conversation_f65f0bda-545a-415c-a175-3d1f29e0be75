"use client";

import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import type { ContentPiece } from "@/lib/server/supabase";
import { 
  ExternalLink, 
  Image as ImageIcon, 
  Eye, 
  Calendar, 
  User, 
  MessageSquare,
  TrendingUp,
  Sparkles,
  ChevronRight,
  ChevronLeft
} from "lucide-react";
import Image from "next/image";

interface TestimonialCarouselCardProps {
  testimonial: ContentPiece;
  className?: string;
  index?: number;
}

export function TestimonialCarouselCard({ 
  testimonial, 
  className = "",
  index = 0 
}: TestimonialCarouselCardProps) {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [isImageModalOpen, setIsImageModalOpen] = useState(false);
  
  const hasScreenshots = testimonial.screenshot_urls && testimonial.screenshot_urls.length > 0;
  
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  return (
    <>
      <div 
        className={`carousel-card-wrapper ${className}`}
        style={{ animationDelay: `${index * 100}ms` }}
      >
        <Card className="carousel-card overflow-hidden h-full relative group shadow-md hover:shadow-xl transition-all duration-500 border-0 bg-gradient-to-br from-card to-card/95 backdrop-blur-sm flex flex-col">        
          {/* Screenshot Section - 80% of card height with fixed aspect ratio */}
          <div className="relative h-80 bg-gradient-to-br from-primary/5 via-muted/10 to-accent/5 overflow-hidden cursor-pointer" onClick={() => hasScreenshots && setIsImageModalOpen(true)}>
            {hasScreenshots ? (
              <>
                {/* Loading shimmer effect */}
                {!imageLoaded && (
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse" />
                )}
                
                <Image
                  src={testimonial.screenshot_urls![selectedImageIndex]}
                  alt={`${testimonial.content_title || 'Testimonial'} screenshot`}
                  fill
                  className={`object-cover transition-all duration-700 ${
                    imageLoaded ? 'scale-100 opacity-100' : 'scale-110 opacity-0'
                  } group-hover:scale-105`}
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  onLoad={() => {
                    setImageLoaded(true);
                    console.log('Image loaded successfully:', testimonial.screenshot_urls![selectedImageIndex]);
                  }}
                  priority={index < 3}
                  onError={(e) => {
                    console.error('Image failed to load:', testimonial.screenshot_urls![selectedImageIndex]);
                    console.error('Error details:', e);
                    // Set imageLoaded to true anyway to show the fallback properly
                    setImageLoaded(true);
                  }}
                  unoptimized
                />
                
                {/* Click to expand overlay */}
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-300 flex items-center justify-center">
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-white/90 backdrop-blur-sm rounded-full p-3">
                    <Eye className="w-6 h-6 text-black" />
                  </div>
                </div>
              </>
            ) : (
              /* Enhanced fallback when no screenshot */
              <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-muted/20 to-muted/5">
                <div className="text-center space-y-3">
                  <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mx-auto">
                    <ImageIcon className="w-8 h-8 text-primary/40" />
                  </div>
                  <p className="text-sm text-muted-foreground/60 font-medium">Preview coming soon</p>
                </div>
              </div>
            )}
            
            {/* Enhanced image navigation dots */}
            {hasScreenshots && testimonial.screenshot_urls!.length > 1 && (
              <div className="absolute bottom-3 left-1/2 -translate-x-1/2 flex gap-1.5 bg-black/30 backdrop-blur-sm rounded-full px-2 py-1">
                {testimonial.screenshot_urls!.map((_, idx) => (
                  <button
                    key={idx}
                    onClick={(e) => {
                      e.stopPropagation();
                      setSelectedImageIndex(idx);
                    }}
                    className={`transition-all duration-300 rounded-full ${
                      idx === selectedImageIndex 
                        ? 'w-6 h-2 bg-white' 
                        : 'w-2 h-2 bg-white/50 hover:bg-white/75'
                    }`}
                    aria-label={`View screenshot ${idx + 1}`}
                  />
                ))}
              </div>
            )}

            {/* Enhanced View button */}
            <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-all duration-300">
              <Button
                size="sm"
                variant="secondary"
                className="bg-white/90 hover:bg-white text-black shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                onClick={(e) => e.stopPropagation()}
                asChild
              >
                <a 
                  href={testimonial.content_link} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="flex items-center gap-1.5"
                >
                  <ExternalLink className="w-3.5 h-3.5" />
                  <span className="text-xs font-medium">View</span>
                </a>
              </Button>
            </div>
          </div>

          {/* Content Section - compact */}
          <div className="p-4 space-y-3">
            {/* Title */}
            <h3 className="font-semibold text-base leading-tight line-clamp-1 text-foreground group-hover:text-primary transition-colors duration-300">
              {testimonial.content_title || "Client Testimonial"}
            </h3>
            
            {/* Company Link */}
            {testimonial.content_link && (
              <div className="flex items-center gap-2">
                <ExternalLink className="w-3.5 h-3.5 text-muted-foreground" />
                <a 
                  href={testimonial.content_link} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-sm text-primary hover:text-primary/80 transition-colors truncate font-medium"
                >
                  View Original Post
                </a>
              </div>
            )}
            
            {/* Author and metrics */}
            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <div className="flex items-center gap-2">
                <div className="w-5 h-5 rounded-full bg-primary/10 flex items-center justify-center">
                  <User className="w-3 h-3 text-primary/60" />
                </div>
                <span className="font-medium truncate max-w-[120px]">{testimonial.host || testimonial.content_account?.[0] || "Anonymous"}</span>
              </div>
              
              {/* Metrics */}
              <div className="flex items-center gap-3">
                {testimonial.content_views > 0 && (
                  <div className="flex items-center gap-1">
                    <Eye className="w-3.5 h-3.5 text-primary/70" />
                    <span className="font-medium">{formatNumber(testimonial.content_views)}</span>
                  </div>
                )}
                {testimonial.twitter_impressions > 0 && (
                  <div className="flex items-center gap-1">
                    <TrendingUp className="w-3.5 h-3.5 text-accent/70" />
                    <span className="font-medium">{formatNumber(testimonial.twitter_impressions)}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Image Modal */}
      {isImageModalOpen && hasScreenshots && (
        <div 
          className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          onClick={() => setIsImageModalOpen(false)}
        >
          <div className="relative max-w-4xl max-h-full w-full">
            {/* Close button */}
            <button
              onClick={() => setIsImageModalOpen(false)}
              className="absolute top-4 right-4 z-10 bg-white/10 backdrop-blur-sm rounded-full p-2 hover:bg-white/20 transition-colors"
            >
              <ExternalLink className="w-6 h-6 text-white rotate-45" />
            </button>
            
            {/* Modal image */}
            <div className="relative aspect-video w-full bg-black rounded-lg overflow-hidden">
              <Image
                src={testimonial.screenshot_urls![selectedImageIndex]}
                alt={`${testimonial.content_title || 'Testimonial'} screenshot`}
                fill
                className="object-contain"
                sizes="100vw"
                priority
              />
            </div>
            
            {/* Image navigation for modal */}
            {testimonial.screenshot_urls!.length > 1 && (
              <>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelectedImageIndex((prev) => 
                      prev === 0 ? testimonial.screenshot_urls!.length - 1 : prev - 1
                    );
                  }}
                  className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/10 backdrop-blur-sm rounded-full p-3 hover:bg-white/20 transition-colors"
                >
                  <ChevronLeft className="w-6 h-6 text-white" />
                </button>
                
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelectedImageIndex((prev) => 
                      prev === testimonial.screenshot_urls!.length - 1 ? 0 : prev + 1
                    );
                  }}
                  className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/10 backdrop-blur-sm rounded-full p-3 hover:bg-white/20 transition-colors"
                >
                  <ChevronRight className="w-6 h-6 text-white" />
                </button>
                
                {/* Dots indicator in modal */}
                <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-2 bg-black/30 backdrop-blur-sm rounded-full px-3 py-2">
                  {testimonial.screenshot_urls!.map((_, idx) => (
                    <button
                      key={idx}
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedImageIndex(idx);
                      }}
                      className={`transition-all duration-300 rounded-full ${
                        idx === selectedImageIndex 
                          ? 'w-8 h-2 bg-white' 
                          : 'w-2 h-2 bg-white/50 hover:bg-white/75'
                      }`}
                      aria-label={`View screenshot ${idx + 1}`}
                    />
                  ))}
                </div>
              </>
            )}
          </div>
        </div>
      )}
    </>
  );
}