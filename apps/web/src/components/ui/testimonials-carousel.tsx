"use client";

import { useCallback, useEffect, useState } from "react";
import useEmblaCarousel from "embla-carousel-react";
import Autoplay from "embla-carousel-autoplay";
import { TestimonialCarouselCard } from "./testimonial-carousel-card";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronLeft, ChevronRight, Pause, Play } from "lucide-react";
import type { ContentPiece } from "@/lib/server/supabase";

interface TestimonialsCarouselProps {
  testimonials: ContentPiece[];
  className?: string;
  autoplayDelay?: number;
}

export function TestimonialsCarousel({ 
  testimonials, 
  className = "",
  autoplayDelay = 5000 
}: TestimonialsCarouselProps) {
  const [isPlaying, setIsPlaying] = useState(true);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [scrollSnaps, setScrollSnaps] = useState<number[]>([]);

  const autoplay = Autoplay({ 
    delay: autoplayDelay,
    stopOnInteraction: false,
    stopOnMouseEnter: true
  });

  const [emblaRef, emblaApi] = useEmblaCarousel(
    {
      loop: true,
      align: "start",
      skipSnaps: false,
      containScroll: "trimSnaps",
      breakpoints: {
        "(max-width: 640px)": { slidesToScroll: 1 },
        "(max-width: 1024px)": { slidesToScroll: 2 },
        "(min-width: 1025px)": { slidesToScroll: 3 }
      }
    },
    [autoplay]
  );

  const scrollPrev = useCallback(() => {
    if (emblaApi) emblaApi.scrollPrev();
  }, [emblaApi]);

  const scrollNext = useCallback(() => {
    if (emblaApi) emblaApi.scrollNext();
  }, [emblaApi]);

  const scrollTo = useCallback((index: number) => {
    if (emblaApi) emblaApi.scrollTo(index);
  }, [emblaApi]);

  const onSelect = useCallback(() => {
    if (!emblaApi) return;
    setSelectedIndex(emblaApi.selectedScrollSnap());
  }, [emblaApi]);

  const toggleAutoplay = useCallback(() => {
    if (!emblaApi) return;
    const autoplay = emblaApi.plugins().autoplay;
    if (!autoplay) return;

    if (isPlaying) {
      autoplay.stop();
    } else {
      autoplay.play();
    }
    setIsPlaying(!isPlaying);
  }, [emblaApi, isPlaying]);

  useEffect(() => {
    if (!emblaApi) return;

    onSelect();
    setScrollSnaps(emblaApi.scrollSnapList());
    emblaApi.on("select", onSelect);
    emblaApi.on("reInit", onSelect);

    return () => {
      emblaApi.off("select", onSelect);
      emblaApi.off("reInit", onSelect);
    };
  }, [emblaApi, onSelect]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "ArrowLeft") {
        event.preventDefault();
        scrollPrev();
      } else if (event.key === "ArrowRight") {
        event.preventDefault();
        scrollNext();
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [scrollPrev, scrollNext]);

  if (!testimonials || testimonials.length === 0) {
    return null;
  }

  return (
    <div className={`testimonials-carousel w-full ${className}`}>
      {/* Full-Width Carousel Container */}
      <div className="relative w-full">
        {/* Main Carousel - Full Width */}
        <div className="overflow-hidden w-full" ref={emblaRef}>
          <div className="flex gap-8 pl-8 pr-8 sm:pl-12 sm:pr-12 lg:pl-16 lg:pr-16">
            {testimonials.map((testimonial, index) => (
              <div 
                key={testimonial.id} 
                className="flex-[0_0_90%] sm:flex-[0_0_45%] lg:flex-[0_0_30%] min-w-0"
              >
                <TestimonialCarouselCard
                  testimonial={testimonial}
                  index={index}
                  className="h-full"
                />
              </div>
            ))}
          </div>
        </div>

        {/* Navigation Buttons - Mathematically positioned for perfect alignment */}
        <div className="absolute inset-y-0 left-0 right-0 flex items-center pointer-events-none z-10">
          {/* Left Arrow - Precisely positioned outside the leftmost visible card */}
          <div className="absolute left-2 sm:left-4 lg:left-6 flex-shrink-0 pointer-events-auto">
            <Button
              variant="outline"
              size="icon"
              className="carousel-nav-button carousel-nav-prev w-12 h-12 rounded-full shadow-2xl hover:shadow-3xl transition-all duration-300 bg-white/90 hover:bg-white dark:bg-gray-900/90 dark:hover:bg-gray-900 backdrop-blur-lg border-2 border-white/20 hover:border-primary/40 hover:scale-110"
              onClick={scrollPrev}
              aria-label="Previous testimonial"
            >
              <ChevronLeft className="w-6 h-6 text-foreground hover:text-primary transition-colors" />
            </Button>
          </div>
          
          {/* Right Arrow - Precisely positioned outside the rightmost visible card */}
          <div className="absolute right-2 sm:right-4 lg:right-6 flex-shrink-0 pointer-events-auto">
            <Button
              variant="outline" 
              size="icon"
              className="carousel-nav-button carousel-nav-next w-12 h-12 rounded-full shadow-2xl hover:shadow-3xl transition-all duration-300 bg-white/90 hover:bg-white dark:bg-gray-900/90 dark:hover:bg-gray-900 backdrop-blur-lg border-2 border-white/20 hover:border-primary/40 hover:scale-110"
              onClick={scrollNext}
              aria-label="Next testimonial"
            >
              <ChevronRight className="w-6 h-6 text-foreground hover:text-primary transition-colors" />
            </Button>
          </div>
        </div>
      </div>

      {/* Controls Section - Cleaner Design */}
      <div className="flex items-center justify-center mt-8">
        {/* Centered Dot Navigation */}
        <div className="flex items-center gap-2 bg-muted/20 backdrop-blur-sm rounded-full px-3 py-1.5">
          {scrollSnaps.map((_, index) => (
            <button
              key={index}
              className={`transition-all duration-300 rounded-full ${
                index === selectedIndex 
                  ? "w-6 h-1.5 bg-foreground/60" 
                  : "w-1.5 h-1.5 bg-muted-foreground/30 hover:bg-muted-foreground/50"
              }`}
              onClick={() => scrollTo(index)}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
          
          {/* Play/Pause Button */}
          <button
            onClick={toggleAutoplay}
            className="ml-2 p-1 rounded-full hover:bg-muted/50 transition-colors"
            aria-label={isPlaying ? "Pause carousel" : "Play carousel"}
          >
            {isPlaying ? (
              <Pause className="w-3 h-3 text-muted-foreground" />
            ) : (
              <Play className="w-3 h-3 text-muted-foreground" />
            )}
          </button>
        </div>
      </div>
    </div>
  );
}