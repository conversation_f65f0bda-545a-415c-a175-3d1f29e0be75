"use client";

import { useState } from "react";
import { trpc } from "@/utils/trpc";
import { useAuth } from "@/components/auth/auth-provider";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { createClient } from "@/utils/supabase/client";

export function AuthDebugPanel() {
  const [isOpen, setIsOpen] = useState(false);
  const [clientSessionData, setClientSessionData] = useState<any>(null);
  const { user: authProviderUser, loading: authLoading } = useAuth();
  
  const getCurrentUserQuery = trpc.auth.getCurrentUser.useQuery(undefined, {
    retry: false,
    refetchOnWindowFocus: false,
  });
  
  const getUserProfileQuery = trpc.auth.getUserProfile.useQuery(undefined, {
    retry: false,
    refetchOnWindowFocus: false,
  });

  const checkClientSession = async () => {
    const supabase = createClient();
    const { data: { session }, error } = await supabase.auth.getSession();
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    setClientSessionData({
      session: {
        hasSession: !!session,
        user: session?.user ? {
          id: session.user.id,
          email: session.user.email,
          created_at: session.user.created_at,
          last_sign_in_at: session.user.last_sign_in_at
        } : null,
        expires_at: session?.expires_at,
        access_token: session?.access_token ? `${session.access_token.substring(0, 20)}...` : null,
        refresh_token: session?.refresh_token ? `${session.refresh_token.substring(0, 20)}...` : null,
        error: error?.message
      },
      user: {
        hasUser: !!user,
        userData: user ? {
          id: user.id,
          email: user.email,
          created_at: user.created_at,
          last_sign_in_at: user.last_sign_in_at
        } : null,
        error: userError?.message
      },
      storage: typeof window !== 'undefined' ? {
        localStorage: Object.keys(localStorage).filter(k => k.includes('supabase')),
        sessionStorage: Object.keys(sessionStorage).filter(k => k.includes('supabase')),
        cookies: document.cookie.split(';').filter(c => c.includes('sb-')).map(c => c.trim())
      } : null
    });
  };

  if (!isOpen) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button 
          onClick={() => setIsOpen(true)}
          variant="outline"
          size="sm"
          className="bg-yellow-100 border-yellow-300 text-yellow-800 hover:bg-yellow-200"
        >
          🐛 Debug Auth
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 w-96 max-h-96 overflow-y-auto">
      <Card className="bg-yellow-50 border-yellow-300">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm text-yellow-800">🐛 Auth Debug Panel</CardTitle>
            <Button 
              onClick={() => setIsOpen(false)}
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
            >
              ✕
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-3 text-xs">
          {/* Auth Provider State */}
          <div>
            <h4 className="font-semibold text-yellow-800">Auth Provider (Client)</h4>
            <div className="bg-white p-2 rounded border">
              <Badge variant={authProviderUser ? "default" : "destructive"}>
                {authProviderUser ? "✅ User Found" : "❌ No User"}
              </Badge>
              {authProviderUser && (
                <div className="mt-1">
                  <div>Email: {authProviderUser.email}</div>
                  <div>ID: {authProviderUser.id}</div>
                </div>
              )}
              <div>Loading: {authLoading ? "Yes" : "No"}</div>
            </div>
          </div>

          {/* tRPC getCurrentUser */}
          <div>
            <h4 className="font-semibold text-yellow-800">tRPC getCurrentUser</h4>
            <div className="bg-white p-2 rounded border">
              <Badge variant={getCurrentUserQuery.data ? "default" : "destructive"}>
                {getCurrentUserQuery.data ? "✅ User Found" : "❌ No User"}
              </Badge>
              {getCurrentUserQuery.data && (
                <div className="mt-1">
                  <div>Email: {getCurrentUserQuery.data.email}</div>
                  <div>ID: {getCurrentUserQuery.data.id}</div>
                </div>
              )}
              <div>Loading: {getCurrentUserQuery.isLoading ? "Yes" : "No"}</div>
              {getCurrentUserQuery.error && (
                <div className="text-red-600">Error: {getCurrentUserQuery.error.message}</div>
              )}
            </div>
          </div>

          {/* tRPC getUserProfile */}
          <div>
            <h4 className="font-semibold text-yellow-800">tRPC getUserProfile</h4>
            <div className="bg-white p-2 rounded border">
              <Badge variant={getUserProfileQuery.data ? "default" : "destructive"}>
                {getUserProfileQuery.data ? "✅ Profile Found" : "❌ No Profile"}
              </Badge>
              {getUserProfileQuery.data && (
                <div className="mt-1">
                  <div>Email: {getUserProfileQuery.data.email}</div>
                  <div>Role: <Badge variant={getUserProfileQuery.data.role === 'admin' ? 'default' : 'secondary'}>{getUserProfileQuery.data.role}</Badge></div>
                  <div>Name: {getUserProfileQuery.data.full_name}</div>
                </div>
              )}
              <div>Loading: {getUserProfileQuery.isLoading ? "Yes" : "No"}</div>
              {getUserProfileQuery.error && (
                <div className="text-red-600">Error: {getUserProfileQuery.error.message}</div>
              )}
            </div>
          </div>

          {/* Client Session Check */}
          <div>
            <div className="flex items-center justify-between">
              <h4 className="font-semibold text-yellow-800">Client Session</h4>
              <Button onClick={checkClientSession} size="sm" variant="outline" className="h-6 text-xs">
                Check
              </Button>
            </div>
            {clientSessionData && (
              <div className="bg-white p-2 rounded border">
                <div>Session: {clientSessionData.session.hasSession ? "✅ Yes" : "❌ No"}</div>
                <div>User: {clientSessionData.session.user ? "✅ Yes" : "❌ No"}</div>
                {clientSessionData.session.user && (
                  <div className="ml-2">
                    <div>Email: {clientSessionData.session.user.email}</div>
                  </div>
                )}
                <div>Cookies: {clientSessionData.storage?.cookies.length || 0}</div>
                <div>LocalStorage: {clientSessionData.storage?.localStorage.length || 0}</div>
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="flex gap-2">
            <Button 
              onClick={() => {
                getCurrentUserQuery.refetch();
                getUserProfileQuery.refetch();
              }}
              size="sm" 
              variant="outline"
              className="text-xs"
            >
              Refetch Queries
            </Button>
            <Button 
              onClick={() => window.location.reload()}
              size="sm" 
              variant="outline"
              className="text-xs"
            >
              Reload Page
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
