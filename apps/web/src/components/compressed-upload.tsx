"use client";

import { useState } from "react";
import { useUploadThing } from "@/lib/uploadthing/components";
import { compressImageBatch, type CompressionResult } from "@/utils/image-compression";
import { Progress } from "@/components/ui/progress";
import { Button } from "@/components/ui/button";
import { Upload, Image, Zap } from "lucide-react";

interface CompressedUploadProps {
  onUploadComplete?: (urls: string[]) => void;
  maxFiles?: number;
  accept?: string;
}

export function CompressedUpload({ 
  onUploadComplete, 
  maxFiles = 5,
  accept = "image/*"
}: CompressedUploadProps) {
  const [isCompressing, setIsCompressing] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [compressionProgress, setCompressionProgress] = useState(0);
  const [compressionStats, setCompressionStats] = useState<CompressionResult[]>([]);

  const { startUpload, isUploading: uploadThingUploading } = useUploadThing("testimonialScreenshots", {
    onClientUploadComplete: (res) => {
      console.log("✅ [Compressed Upload] Upload completed:", res);
      const urls = (res ?? []).map(file => file.url || (file as any)?.ufsUrl).filter(Boolean) as string[];
      onUploadComplete?.(urls);
      setIsUploading(false);
      setCompressionStats([]);
    },
    onUploadError: (error) => {
      console.error("❌ [Compressed Upload] Upload failed:", error);
      setIsUploading(false);
    },
  });

  const handleFileSelection = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    if (files.length === 0) return;

    try {
      // Step 1: Compress images
      setIsCompressing(true);
      setCompressionProgress(0);
      
      const compressionResults = await compressImageBatch(files, (completed, total) => {
        setCompressionProgress((completed / total) * 100);
      });
      
      setCompressionStats(compressionResults);
      setIsCompressing(false);
      
      // Step 2: Upload compressed files
      setIsUploading(true);
      const compressedFiles = compressionResults.map(result => result.file);
      await startUpload(compressedFiles);
      
    } catch (error) {
      console.error("❌ [Compressed Upload] Compression or upload failed:", error);
      setIsCompressing(false);
      setIsUploading(false);
    }
  };

  const totalOriginalSize = compressionStats.reduce((sum, stat) => sum + stat.originalSize, 0);
  const totalCompressedSize = compressionStats.reduce((sum, stat) => sum + stat.compressedSize, 0);
  const totalSavings = totalOriginalSize - totalCompressedSize;
  const averageCompression = totalOriginalSize > 0 ? (totalSavings / totalOriginalSize) * 100 : 0;

  return (
    <div className="space-y-6">
      {/* Upload Button */}
      <div className="flex flex-col items-center justify-center border-2 border-dashed border-border rounded-lg p-8 hover:border-primary/50 transition-colors">
        <div className="flex flex-col items-center space-y-4">
          <div className="flex items-center space-x-2">
            <Upload className="w-8 h-8 text-muted-foreground" />
            <Zap className="w-6 h-6 text-primary" />
          </div>
          
          <div className="text-center">
            <h3 className="text-lg font-semibold">Smart Compressed Upload</h3>
            <p className="text-sm text-muted-foreground mt-1">
              Images will be automatically compressed before upload
            </p>
          </div>
          
          <Button asChild disabled={isCompressing || isUploading}>
            <label className="cursor-pointer">
              <input
                type="file"
                multiple
                accept={accept}
                onChange={handleFileSelection}
                className="sr-only"
                max={maxFiles}
              />
              {isCompressing ? "Compressing..." : isUploading ? "Uploading..." : "Select Images"}
            </label>
          </Button>
        </div>
      </div>

      {/* Compression Progress */}
      {isCompressing && (
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span>Compressing images...</span>
            <span>{compressionProgress.toFixed(0)}%</span>
          </div>
          <Progress value={compressionProgress} className="w-full" />
        </div>
      )}

      {/* Compression Stats */}
      {compressionStats.length > 0 && !isCompressing && (
        <div className="bg-muted/50 rounded-lg p-4 space-y-3">
          <div className="flex items-center space-x-2">
            <Zap className="w-5 h-5 text-primary" />
            <h4 className="font-semibold">Compression Results</h4>
          </div>
          
          <div className="grid grid-cols-3 gap-4 text-sm">
            <div className="text-center">
              <div className="font-semibold text-lg">
                {(totalOriginalSize / 1024 / 1024).toFixed(1)}MB
              </div>
              <div className="text-muted-foreground">Original</div>
            </div>
            
            <div className="text-center">
              <div className="font-semibold text-lg text-primary">
                {(totalCompressedSize / 1024 / 1024).toFixed(1)}MB
              </div>
              <div className="text-muted-foreground">Compressed</div>
            </div>
            
            <div className="text-center">
              <div className="font-semibold text-lg text-green-600">
                -{averageCompression.toFixed(1)}%
              </div>
              <div className="text-muted-foreground">Saved</div>
            </div>
          </div>
          
          <div className="text-xs text-muted-foreground text-center">
            💾 Bandwidth saved: {(totalSavings / 1024 / 1024).toFixed(1)}MB
          </div>
        </div>
      )}

      {/* Individual File Stats */}
      {compressionStats.length > 0 && !isCompressing && (
        <div className="space-y-2">
          <h5 className="font-medium text-sm">File Details:</h5>
          <div className="space-y-1">
            {compressionStats.map((stat, index) => (
              <div key={index} className="flex items-center justify-between text-xs bg-card p-2 rounded">
                <div className="flex items-center space-x-2">
                  <Image className="w-4 h-4" />
                  <span className="truncate max-w-32">{stat.file.name}</span>
                </div>
                <div className="text-right">
                  <div className="text-muted-foreground">
                    {(stat.originalSize / 1024).toFixed(0)}KB → {(stat.compressedSize / 1024).toFixed(0)}KB
                  </div>
                  {stat.compressionRatio > 0 && (
                    <div className="text-green-600 font-medium">
                      -{stat.compressionRatio.toFixed(1)}%
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}