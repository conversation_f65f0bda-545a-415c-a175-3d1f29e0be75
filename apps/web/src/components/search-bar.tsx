"use client";

import { useState, useEffect } from "react";
import { Input } from "./ui/input";
import { Button } from "./ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from "./ui/dropdown-menu";
import { trpc } from "@/utils/trpc";
import { 
  Search, 
  Filter, 
  SortDesc, 
  SortAsc,
  TrendingUp, 
  Calendar, 
  MessageCircle, 
  Users, 
  Twitter,
  Megaphone,
  Zap,
  Heart,
  Repeat2,
  Eye,
  Hash,
  X,
  Sparkles,
  FileText,
  Radio
} from "lucide-react";

interface Filters {
  contentTypes: string[];
  categories: string[];
  sortBy: string;
  sortOrder: string;
}

interface SearchBarProps {
  onSearch: (query: string) => void;
  onFilter: (filterType: string, values: string[]) => void;
  onSort: (sortBy: string, sortOrder?: string) => void;
  placeholder?: string;
  filters: Filters;
  searchQuery: string;
}

// Icon mapping for content types
const getContentTypeIcon = (type: string) => {
  const iconMap: Record<string, React.ReactElement> = {
    twitter: <Twitter className="w-4 h-4" />,
    spaces: <Radio className="w-4 h-4" />,
    tweets: <Twitter className="w-4 h-4" />,
    marketing: <Megaphone className="w-4 h-4" />,
    presskit: <FileText className="w-4 h-4" />,
    incubation: <Users className="w-4 h-4" />,
    testimonials: <Heart className="w-4 h-4" />,
  };
  return iconMap[type.toLowerCase()] || <Sparkles className="w-4 h-4" />;
};

// Format content type label
const formatLabel = (value: string) => {
  return value
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

const sortOptions = [
  { id: "impressions", label: "Impressions", icon: <Eye className="w-4 h-4" /> },
  { id: "date", label: "Date", icon: <Calendar className="w-4 h-4" /> },
  { id: "likes", label: "Likes", icon: <Heart className="w-4 h-4" /> },
  { id: "retweets", label: "Retweets", icon: <Repeat2 className="w-4 h-4" /> },
];

export function SearchBar({ onSearch, onFilter, onSort, placeholder = "Search content...", filters, searchQuery }: SearchBarProps) {
  const [query, setQuery] = useState(searchQuery);
  const [debouncedQuery, setDebouncedQuery] = useState(searchQuery);
  
  // Fetch content types and categories from database
  const { data: filterData } = trpc.content.public.getContentFilters.useQuery(undefined, {
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    gcTime: 10 * 60 * 1000, // Keep in cache for 10 minutes (renamed from cacheTime in v5)
  });

  // Use dynamic data with fallback to empty arrays
  const contentTypes = (filterData?.contentTypes || []).map(type => {
    const countData = filterData?.contentTypesWithCounts?.find(item => item.type === type);
    return {
      id: type,
      label: formatLabel(type),
      icon: getContentTypeIcon(type),
      count: countData?.count || 0
    };
  });

  const categories = (filterData?.categories || []).map(cat => {
    const countData = filterData?.categoriesWithCounts?.find(item => item.category === cat);
    return {
      id: cat,
      label: formatLabel(cat),
      count: countData?.count || 0
    };
  });

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(query);
    }, 300);

    return () => clearTimeout(timer);
  }, [query]);

  // Sync internal state with prop changes (for browser navigation)
  useEffect(() => {
    setQuery(searchQuery);
    setDebouncedQuery(searchQuery);
  }, [searchQuery]);

  // Call onSearch when debounced query changes
  useEffect(() => {
    onSearch(debouncedQuery);
  }, [debouncedQuery, onSearch]);

  const getActiveFiltersCount = () => {
    let count = 0;
    count += filters.contentTypes.length;
    count += filters.categories.length;
    return count;
  };

  const clearAllFilters = () => {
    onFilter("contentTypes", []);
    onFilter("categories", []);
    setQuery("");
  };

  const toggleContentType = (typeId: string) => {
    const currentTypes = filters.contentTypes;
    const newTypes = currentTypes.includes(typeId)
      ? currentTypes.filter(t => t !== typeId)
      : [...currentTypes, typeId];
    onFilter("contentTypes", newTypes);
  };

  const toggleCategory = (categoryId: string) => {
    const currentCategories = filters.categories;
    const newCategories = currentCategories.includes(categoryId)
      ? currentCategories.filter(c => c !== categoryId)
      : [...currentCategories, categoryId];
    onFilter("categories", newCategories);
  };

  return (
    <div className="w-full space-y-6">
      {/* Search Input */}
      <div className="relative flex justify-center">
        <div className="relative w-full max-w-2xl">
          <Search className="absolute left-5 top-1/2 transform -translate-y-1/2 w-6 h-6 text-foreground z-10 pointer-events-none" />
          <Input
            type="text"
            placeholder={placeholder}
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            className="pl-16 pr-6 h-16 text-lg bg-background/95 backdrop-blur-sm border-2 border-border/50 focus:border-primary/50 focus:ring-2 focus:ring-primary/20 transition-all duration-300 font-medium placeholder:text-muted-foreground/80 shadow-lg rounded-xl"
          />
        </div>
      </div>

      {/* Filter Row */}
      <div className="flex gap-3 flex-wrap items-center justify-center">
        {/* Content Type Filter */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button 
              variant="outline" 
              size="sm"
              className={`gap-2 glass-effect border-0 font-medium ${
                filters.contentTypes.length > 0 ? "bg-primary/10 text-primary" : ""
              }`}
            >
              <Filter className="w-4 h-4" />
              Content Types
              {filters.contentTypes.length > 0 && (
                <span className="bg-primary text-primary-foreground rounded-full px-2 py-0.5 text-xs font-bold">
                  {filters.contentTypes.length}
                </span>
              )}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56 glass-effect border-0 shadow-xl">
            <div className="px-3 py-2 text-xs font-bold text-muted-foreground uppercase tracking-wider">
              Content Types
            </div>
            {contentTypes.map((type) => (
              <DropdownMenuItem 
                key={type.id}
                onClick={(e) => {
                  e.preventDefault();
                  toggleContentType(type.id);
                }}
                className={`gap-3 py-3 px-3 cursor-pointer rounded-lg mb-1 font-medium ${
                  filters.contentTypes.includes(type.id) ? "bg-primary/10 text-primary" : ""
                }`}
              >
                <div className="flex items-center gap-3 w-full">
                  {type.icon}
                  <span className="flex-1">{type.label}</span>
                  <span className="text-xs text-muted-foreground bg-muted px-2 py-0.5 rounded-full">
                    {type.count}
                  </span>
                  {filters.contentTypes.includes(type.id) && (
                    <div className="w-2 h-2 bg-primary rounded-full ml-2" />
                  )}
                </div>
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Category Filter */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button 
              variant="outline" 
              size="sm"
              className={`gap-2 glass-effect border-0 font-medium ${
                filters.categories.length > 0 ? "bg-primary/10 text-primary" : ""
              }`}
            >
              <Hash className="w-4 h-4" />
              Categories
              {filters.categories.length > 0 && (
                <span className="bg-primary text-primary-foreground rounded-full px-2 py-0.5 text-xs font-bold">
                  {filters.categories.length}
                </span>
              )}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56 glass-effect border-0 shadow-xl max-h-80 overflow-y-auto scrollbar-styled">
            <div className="px-3 py-2 text-xs font-bold text-muted-foreground uppercase tracking-wider">
              Categories
            </div>
            {categories.map((category) => (
              <DropdownMenuItem 
                key={category.id}
                onClick={(e) => {
                  e.preventDefault();
                  toggleCategory(category.id);
                }}
                className={`gap-3 py-3 px-3 cursor-pointer rounded-lg mb-1 font-medium ${
                  filters.categories.includes(category.id) ? "bg-primary/10 text-primary" : ""
                }`}
              >
                <div className="flex items-center gap-3 w-full">
                  <Hash className="w-4 h-4" />
                  <span className="flex-1">{category.label}</span>
                  <span className="text-xs text-muted-foreground bg-muted px-2 py-0.5 rounded-full">
                    {category.count}
                  </span>
                  {filters.categories.includes(category.id) && (
                    <div className="w-2 h-2 bg-primary rounded-full ml-2" />
                  )}
                </div>
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Sort Options */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button 
              variant="outline" 
              size="sm"
              className="gap-2 glass-effect border-0 font-medium"
            >
              {filters.sortOrder === "desc" ? <SortDesc className="w-4 h-4" /> : <SortAsc className="w-4 h-4" />}
              {sortOptions.find(s => s.id === filters.sortBy)?.label}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56 glass-effect border-0 shadow-xl">
            <div className="px-3 py-2 text-xs font-bold text-muted-foreground uppercase tracking-wider">
              Sort By
            </div>
            {sortOptions.map((option) => (
              <DropdownMenuItem 
                key={option.id}
                onClick={() => onSort(option.id)}
                className={`gap-3 py-3 px-3 cursor-pointer rounded-lg mb-1 font-medium ${
                  filters.sortBy === option.id ? "bg-primary/10 text-primary" : ""
                }`}
              >
                {option.icon}
                {option.label}
              </DropdownMenuItem>
            ))}
            <DropdownMenuSeparator className="my-2" />
            <DropdownMenuItem 
              onClick={() => onSort(filters.sortBy, filters.sortOrder === "desc" ? "asc" : "desc")}
              className="gap-3 py-3 px-3 cursor-pointer rounded-lg font-medium"
            >
              {filters.sortOrder === "desc" ? <SortAsc className="w-4 h-4" /> : <SortDesc className="w-4 h-4" />}
              {filters.sortOrder === "desc" ? "Sort Ascending" : "Sort Descending"}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Clear Filters */}
        {(getActiveFiltersCount() > 0 || query.length > 0) && (
          <Button 
            variant="ghost" 
            size="sm"
            onClick={clearAllFilters}
            className="gap-2 text-muted-foreground hover:text-foreground"
          >
            <X className="w-4 h-4" />
            Clear ({getActiveFiltersCount() + (query.length > 0 ? 1 : 0)})
          </Button>
        )}
      </div>
    </div>
  );
}