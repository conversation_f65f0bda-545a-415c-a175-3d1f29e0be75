import { useRef, useState } from "react";
import { trpc } from "@/utils/trpc";
import { toast } from "sonner";
import type { ContentFormData, FormErrors } from "@/types/admin";
import { contentFormSchema } from "@/lib/schemas/content";
import type { ContentPiece } from "@/lib/server/supabase";

const initialFormData: ContentFormData = {
  content_link: "",
  content_tags: [],
  host: "",
  content_account: [""],
  content_created_date: new Date().toISOString().split('T')[0],
  content_types: [],
  twitter_content_type: null,
  content_views: 0,
  content_listeners: 0,
  twitter_impressions: 0,
  content_follow_increase: 0,
  content_title: null,
  content_description: null,
  content_categories: [],
};

export const useContentManagement = (currentPage: number, pageSize: number) => {
  const [formData, setFormData] = useState<ContentFormData>(initialFormData);
  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [editingContent, setEditingContent] = useState<(ContentFormData & { id: number }) | null>(null);
  const [isEditing, setIsEditing] = useState(false);

  // Get content for admin management
  const contentQuery = trpc.content.public.getAllContent.useQuery({
    page: currentPage,
    limit: pageSize,
  });

  // Keep a reference to the last deleted item for undo
  const lastDeletedRef = useRef<ContentPiece | null>(null);

  // Mutations
  const createContentMutation = trpc.content.admin.create.useMutation({
    onSuccess: () => {
      toast.success("Content created successfully!");
      setFormData(initialFormData);
      setFormErrors({});
      contentQuery.refetch();
    },
    onError: (error) => {
      toast.error(`Failed to create content: ${error.message}`);
    },
  });

  const deleteContentMutation = trpc.content.admin.delete.useMutation({
    onMutate: async (variables) => {
      console.log('[Admin/Delete] onMutate', variables);
      try {
        const item = contentQuery.data?.data?.find((c: any) => c.id === variables.id) as ContentPiece | undefined;
        if (item) {
          lastDeletedRef.current = item;
          console.log('[Admin/Delete] Cached item for undo onMutate', { id: item.id });
        }
      } catch (e) {
        console.warn('[Admin/Delete] Failed caching item onMutate', e);
      }
    },
    onSuccess: (_data, variables) => {
      console.log('[Admin/Delete] onSuccess', variables);
      toast.success("Content deleted", {
        action: {
          label: 'Undo',
          onClick: async () => {
            try {
              console.log('[Admin/Delete] Undo clicked for id', variables.id);
              if (lastDeletedRef.current) {
                const { id, created_at, updated_at, ...rest } = lastDeletedRef.current as any;
                const originalMutate = createContentMutation.mutateAsync;
                Object.defineProperty(createContentMutation, 'mutateAsync', {
                  value: async (data: any) => {
                    const result = await originalMutate(data);
                    return result;
                  },
                  configurable: true
                });
                await createContentMutation.mutateAsync(rest as any);
                Object.defineProperty(createContentMutation, 'mutateAsync', {
                  value: originalMutate,
                  configurable: true
                });
                toast.success('Restored');
              } else {
                console.warn('[Admin/Delete] No cached data to restore');
                toast.error('Nothing to restore');
              }
            } catch (e) {
              console.error('[Admin/Delete] Undo error', e);
              toast.error('Failed to restore');
            }
          }
        }
      });
      contentQuery.refetch();
    },
    onError: (error, variables) => {
      console.error('[Admin/Delete] onError', { error, variables });
      toast.error(`Failed to delete content: ${error.message}`);
    },
    onSettled: () => {
      console.log('[Admin/Delete] onSettled');
    }
  });

  const updateContentMutation = trpc.content.admin.update.useMutation({
    onSuccess: () => {
      toast.success("Content updated successfully!");
      setEditingContent(null);
      setIsEditing(false);
      setFormData(initialFormData);
      setFormErrors({});
      contentQuery.refetch();
    },
    onError: (error) => {
      toast.error(`Failed to update content: ${error.message}`);
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Clear previous errors
    setFormErrors({});

    // Validate form data
    const result = contentFormSchema.safeParse(formData);

    if (!result.success) {
      // Extract and set validation errors
      const errors: FormErrors = {};
      result.error.issues.forEach((issue) => {
        const fieldName = issue.path[0] as keyof ContentFormData;
        errors[fieldName] = issue.message;
      });
      setFormErrors(errors);
      toast.error("Please fix the validation errors before submitting.");
      return;
    }

    // Submit validated data (schema already sanitized it)
    if (isEditing && editingContent) {
      updateContentMutation.mutate({
        id: editingContent.id,
        ...result.data
      });
    } else {
      createContentMutation.mutate(result.data);
    }
  };

  const handleEdit = (content: any) => {
    const editData = {
      id: content.id,
      content_link: content.content_link || "",
      content_tags: content.content_tags || [],
      host: content.host || "",
      content_account: Array.isArray(content.content_account) ? content.content_account : [content.content_account || ""],
      content_created_date: content.content_created_date ? new Date(content.content_created_date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
      content_types: content.content_types || [],
      twitter_content_type: content.twitter_content_type || null,
      content_views: content.content_views || 0,
      content_listeners: content.content_listeners || 0,
      twitter_impressions: content.twitter_impressions || 0,
      content_follow_increase: content.content_follow_increase || 0,
      content_title: content.content_title || null,
      content_description: content.content_description || null,
      content_categories: content.content_categories || [],
    };

    setEditingContent(editData);
    setFormData(editData);
    setIsEditing(true);
  };

  const handleCancelEdit = () => {
    setEditingContent(null);
    setIsEditing(false);
    setFormData(initialFormData);
    setFormErrors({});
  };

  const handleDelete = (id: number) => {
    console.log('[Admin/Delete] handleDelete called', { id });
    const item = contentQuery.data?.data?.find((c: any) => c.id === id) as ContentPiece | undefined;
    if (item) {
      lastDeletedRef.current = item;
      console.log('[Admin/Delete] Cached deleted item for undo', { id: item.id });
    }
    deleteContentMutation.mutate({ id });
  };

  const resetForm = () => {
    setFormData(initialFormData);
    setFormErrors({});
    setEditingContent(null);
    setIsEditing(false);
  };

  return {
    formData,
    setFormData,
    formErrors,
    editingContent,
    isEditing,
    contentQuery,
    createContentMutation,
    updateContentMutation,
    deleteContentMutation,
    handleSubmit,
    handleEdit,
    handleCancelEdit,
    handleDelete,
    resetForm,
    initialFormData
  };
};