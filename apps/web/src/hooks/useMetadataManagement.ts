import { trpc } from "@/utils/trpc";
import { toast } from "sonner";

export const useMetadataManagement = (activeTab: 'content' | 'metadata' | 'testimonials', showForm: boolean) => {
  // Get metadata for management (fetch when metadata tab is active or when form is shown)
  const metadataQuery = trpc.content.metadata.getFilterMetadata.useQuery({}, {
    enabled: activeTab === 'metadata' || showForm
  });

  // Metadata management mutations
  const renameFilterMutation = trpc.content.metadata.renameFilterValue.useMutation({
    onSuccess: (data) => {
      toast.success(`Successfully renamed value. ${data.updatedCount} content pieces updated.`);
      metadataQuery.refetch();
    },
    onError: (error) => {
      toast.error(`Failed to rename value: ${error.message}`);
    },
  });

  const mergeFilterMutation = trpc.content.metadata.mergeFilterValues.useMutation({
    onSuccess: (data) => {
      toast.success(`Successfully merged values. ${data.updatedCount} content pieces updated.`);
      metadataQuery.refetch();
    },
    onError: (error) => {
      toast.error(`Failed to merge values: ${error.message}`);
    },
  });

  const deleteFilterMutation = trpc.content.metadata.checkFilterValueUsage.useMutation({
    onSuccess: (data) => {
      toast.info(`Usage check: ${data.message}`);
      metadataQuery.refetch();
    },
    onError: (error) => {
      toast.error(`Failed to check value usage: ${error.message}`);
    },
  });

  // Add content type mutation
  const addContentTypeMutation = trpc.content.metadata.addContentType.useMutation({
    onSuccess: (data) => {
      toast.success(data.message);
      metadataQuery.refetch();
    },
    onError: (error) => {
      toast.error(`Failed to add content type: ${error.message}`);
    },
  });

  // Add content category mutation
  const addContentCategoryMutation = trpc.content.metadata.addContentCategory.useMutation({
    onSuccess: (data) => {
      toast.success(data.message);
      metadataQuery.refetch();
    },
    onError: (error) => {
      toast.error(`Failed to add content category: ${error.message}`);
    },
  });

  // Delete content type mutation
  const deleteContentTypeMutation = trpc.content.metadata.deleteContentType.useMutation({
    onSuccess: (data) => {
      toast.success(data.message);
      metadataQuery.refetch();
    },
    onError: (error) => {
      toast.error(`Failed to delete content type: ${error.message}`);
    },
  });

  // Delete content category mutation
  const deleteContentCategoryMutation = trpc.content.metadata.deleteContentCategory.useMutation({
    onSuccess: (data) => {
      toast.success(data.message);
      metadataQuery.refetch();
    },
    onError: (error) => {
      toast.error(`Failed to delete content category: ${error.message}`);
    },
  });

  // Remove content tag mutation
  const removeContentTagMutation = trpc.content.metadata.removeContentTag.useMutation({
    onSuccess: (data) => {
      toast.success(data.message);
      metadataQuery.refetch();
    },
    onError: (error) => {
      toast.error(`Failed to remove tag: ${error.message}`);
    },
  });

  // Add Twitter content type mutation
  const addTwitterContentTypeMutation = trpc.content.metadata.addTwitterContentType.useMutation({
    onSuccess: (data) => {
      toast.success(data.message);
      metadataQuery.refetch();
    },
    onError: (error) => {
      toast.error(`Failed to add Twitter content type: ${error.message}`);
    },
  });

  // Delete Twitter content type mutation
  const deleteTwitterContentTypeMutation = trpc.content.metadata.deleteTwitterContentType.useMutation({
    onSuccess: (data) => {
      toast.success(data.message);
      metadataQuery.refetch();
    },
    onError: (error) => {
      toast.error(`Failed to delete Twitter content type: ${error.message}`);
    },
  });

  // Wrapper functions for inline metadata creation
  const handleAddContentType = async (value: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      addContentTypeMutation.mutate(
        { value },
        {
          onSuccess: () => {
            // Refresh the metadata to get updated enum values
            metadataQuery.refetch();
            resolve();
          },
          onError: (error) => {
            reject(error);
          }
        }
      );
    });
  };

  const handleAddContentCategory = async (value: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      addContentCategoryMutation.mutate(
        { value },
        {
          onSuccess: () => {
            // Refresh the metadata to get updated enum values
            metadataQuery.refetch();
            resolve();
          },
          onError: (error) => {
            reject(error);
          }
        }
      );
    });
  };

  return {
    metadataQuery,
    renameFilterMutation,
    mergeFilterMutation,
    deleteFilterMutation,
    addContentTypeMutation,
    addContentCategoryMutation,
    deleteContentTypeMutation,
    deleteContentCategoryMutation,
    removeContentTagMutation,
    addTwitterContentTypeMutation,
    deleteTwitterContentTypeMutation,
    handleAddContentType,
    handleAddContentCategory,
  };
};