import { trpc } from "@/utils/trpc";

export const useAdminAuth = () => {
  // Use the same authentication method as the header for consistency
  const { data: user, isLoading: userLoading } = trpc.auth.getCurrentUser.useQuery(undefined, {
    retry: 1,
    staleTime: 5 * 60 * 1000,
    refetchOnWindowFocus: false,
  });

  // Check if user is admin - now safe for unauthenticated users
  const userProfileQuery = trpc.auth.getUserProfile.useQuery(undefined, {
    retry: false,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });

  // Comprehensive debugging (development only)
  if (process.env.NODE_ENV === 'development') {
    console.log("🔍 [ADMIN AUTH] ==================== ADMIN AUTH STATE ====================");
    console.log("🔍 [ADMIN AUTH] getCurrentUser query:", {
      hasUser: !!user,
      userEmail: user?.email,
      userId: user?.id,
      userLoading,
      userError: userLoading ? 'Loading...' : (!user ? 'No user' : 'User found')
    });

    console.log("🔍 [ADMIN AUTH] getUserProfile query:", {
      profileLoading: userProfileQuery.isLoading,
      hasProfileData: !!userProfileQuery.data,
      profileData: userProfileQuery.data,
      profileError: userProfileQuery.error?.message,
      profileErrorCode: userProfileQuery.error?.data?.code,
      userRole: userProfileQuery.data?.role,
      queryStatus: userProfileQuery.status,
      queryFetchStatus: userProfileQuery.fetchStatus
    });

    // Additional debugging for profile data
    if (userProfileQuery.data) {
      console.log("✅ [ADMIN AUTH] Profile data found:", userProfileQuery.data);
    } else if (userProfileQuery.error) {
      console.log("❌ [ADMIN AUTH] Profile query error details:", {
        message: userProfileQuery.error.message,
        data: userProfileQuery.error.data,
        shape: userProfileQuery.error.shape
      });
    } else if (!userProfileQuery.isLoading) {
      console.log("⚠️ [ADMIN AUTH] Profile query returned null (no error, no data)");
    }

    // Debug localStorage/sessionStorage
    if (typeof window !== 'undefined') {
      console.log("🔍 [ADMIN AUTH] Browser storage:", {
        localStorage: Object.keys(localStorage).filter(k => k.includes('supabase')),
        sessionStorage: Object.keys(sessionStorage).filter(k => k.includes('supabase')),
        cookies: document.cookie.split(';').filter(c => c.includes('sb-')).map(c => c.trim().split('=')[0])
      });
    }
  }

  const isLoading = userLoading || userProfileQuery.isLoading;
  const isAuthenticated = !!user;
  const isAdmin = !!(userProfileQuery.data && userProfileQuery.data.role === 'admin');

  return {
    user,
    userProfile: userProfileQuery.data,
    isLoading,
    isAuthenticated,
    isAdmin,
    error: userProfileQuery.error
  };
};