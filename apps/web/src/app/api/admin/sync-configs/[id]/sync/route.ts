import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '../../../../../../lib/server/supabase';
import { sheetsSyncService } from '../../../../../../lib/services/sheets-sync';

// Helper function to check if user is admin
async function isUserAdmin(): Promise<boolean> {
  try {
    const supabase = await createServerSupabaseClient();
    
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return false;
    }
    
    // Check if user has admin role
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('role')
      .eq('user_id', user.id)
      .single();
    
    if (profileError || !profile) {
      return false;
    }
    
    return profile.role === 'admin';
  } catch (error) {
    console.error('Error checking admin status:', error);
    return false;
  }
}

interface RouteContext {
  params: Promise<{
    id: string;
  }>;
}

// POST - Manually trigger sync for specific configuration
export async function POST(
  request: NextRequest,
  context: RouteContext
) {
  try {
    // Check if user is admin
    const adminCheck = await isUserAdmin();
    if (!adminCheck) {
      return NextResponse.json({ error: 'Unauthorized - Admin access required' }, { status: 403 });
    }
    
    const supabase = await createServerSupabaseClient();
    const { id } = await context.params;
    
    if (!id) {
      return NextResponse.json({ error: 'Configuration ID is required' }, { status: 400 });
    }
    
    // Parse request body for options
    const body = await request.json().catch(() => ({}));
    const { dryRun = false } = body;
    
    // Get the sync configuration
    const { data: config, error: configError } = await supabase
      .from('spreadsheet_sync_configs')
      .select('*')
      .eq('id', id)
      .single();
    
    if (configError) {
      if (configError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Configuration not found' }, { status: 404 });
      }
      console.error('Failed to fetch sync configuration:', configError);
      return NextResponse.json(
        { error: 'Failed to fetch sync configuration' },
        { status: 500 }
      );
    }
    
    if (!config.is_active) {
      return NextResponse.json(
        { error: 'Cannot sync inactive configuration' },
        { status: 400 }
      );
    }
    
    const startTime = Date.now();
    let logId: string | null = null;
    
    try {
      console.log(`🔄 Starting manual sync for config: ${config.name} (${config.spreadsheet_id}/${config.tab_name})`);
      
      // Create sync log entry
      const { data: logEntry, error: logError } = await supabase
        .from('sync_logs')
        .insert([{
          config_id: config.id,
          started_at: new Date().toISOString(),
          status: 'running',
          records_created: 0,
          records_updated: 0,
          records_skipped: 0,
          error_count: 0
        }])
        .select('id')
        .single();
      
      if (logError) {
        console.warn('Failed to create sync log, continuing without logging:', logError);
      } else {
        logId = logEntry.id;
      }
      
      // Perform the actual sync
      const result = await sheetsSyncService.sync({
        useGoogleSheets: true,
        googleSheetsId: config.spreadsheet_id,
        googleSheetsTab: config.tab_name,
        dryRun
      });
      
      const duration = Date.now() - startTime;
      const completedAt = new Date().toISOString();
      
      // Update sync log with results
      if (logId) {
        await supabase
          .from('sync_logs')
          .update({
            completed_at: completedAt,
            status: 'completed',
            records_created: result.created,
            records_updated: result.updated,
            records_skipped: result.skipped,
            error_count: result.errors.length,
            errors: result.errors.length > 0 ? result.errors : null,
            details: {
              totalProcessed: result.totalProcessed,
              dryRun
            },
            duration_ms: duration
          })
          .eq('id', logId);
      }
      
      console.log(`✅ Manual sync completed for config: ${config.name} (${duration}ms)`);
      
      return NextResponse.json({
        success: true,
        duration,
        timestamp: completedAt,
        dryRun,
        config: {
          id: config.id,
          name: config.name,
          spreadsheet_id: config.spreadsheet_id,
          tab_name: config.tab_name
        },
        result: {
          totalProcessed: result.totalProcessed,
          created: result.created,
          updated: result.updated,
          skipped: result.skipped,
          errorCount: result.errors.length,
          errors: result.errors.slice(0, 10) // Limit errors in response
        }
      });
      
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      console.error(`❌ Manual sync failed for config: ${config.name}`, error);
      
      // Update sync log with error
      if (logId) {
        try {
          await supabase
            .from('sync_logs')
            .update({
              completed_at: new Date().toISOString(),
              status: 'failed',
              error_count: 1,
              errors: [errorMessage],
              duration_ms: duration
            })
            .eq('id', logId);
        } catch (logError) {
          console.error('Failed to update sync log with error:', logError);
        }
      }
      
      return NextResponse.json({
        success: false,
        error: errorMessage,
        duration,
        timestamp: new Date().toISOString(),
        config: {
          id: config.id,
          name: config.name,
          spreadsheet_id: config.spreadsheet_id,
          tab_name: config.tab_name
        }
      }, { status: 500 });
    }
    
  } catch (error) {
    console.error('Manual sync API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}