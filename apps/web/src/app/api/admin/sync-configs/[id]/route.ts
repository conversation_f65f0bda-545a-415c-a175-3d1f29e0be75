import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '../../../../../lib/server/supabase';
import { z } from 'zod';

// Validation schemas
const updateSyncConfigSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name must be less than 100 characters').optional(),
  spreadsheet_id: z.string().min(1, 'Spreadsheet ID is required').optional(),
  tab_name: z.string().min(1, 'Tab name is required').optional(),
  sync_order: z.number().int().min(0).optional(),
  is_active: z.boolean().optional(),
});

// Helper function to check if user is admin
async function isUserAdmin(): Promise<boolean> {
  try {
    const supabase = await createServerSupabaseClient();
    
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return false;
    }
    
    // Check if user has admin role
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('role')
      .eq('user_id', user.id)
      .single();
    
    if (profileError || !profile) {
      return false;
    }
    
    return profile.role === 'admin';
  } catch (error) {
    console.error('Error checking admin status:', error);
    return false;
  }
}

interface RouteContext {
  params: Promise<{
    id: string;
  }>;
}

// GET - Get specific sync configuration by ID
export async function GET(
  request: NextRequest,
  context: RouteContext
) {
  try {
    // Check if user is admin
    const adminCheck = await isUserAdmin();
    if (!adminCheck) {
      return NextResponse.json({ error: 'Unauthorized - Admin access required' }, { status: 403 });
    }
    
    const supabase = await createServerSupabaseClient();
    const { id } = await context.params;
    
    if (!id) {
      return NextResponse.json({ error: 'Configuration ID is required' }, { status: 400 });
    }
    
    const { data, error } = await supabase
      .from('spreadsheet_sync_configs')
      .select(`
        id,
        name,
        spreadsheet_id,
        tab_name,
        is_active,
        last_sync_at,
        last_sync_status,
        last_sync_details,
        sync_order,
        created_at,
        updated_at,
        created_by
      `)
      .eq('id', id)
      .single();
    
    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Configuration not found' }, { status: 404 });
      }
      console.error('Failed to fetch sync configuration:', error);
      return NextResponse.json(
        { error: 'Failed to fetch sync configuration' },
        { status: 500 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Sync config GET error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT - Update sync configuration
export async function PUT(
  request: NextRequest,
  context: RouteContext
) {
  try {
    // Check if user is admin
    const adminCheck = await isUserAdmin();
    if (!adminCheck) {
      return NextResponse.json({ error: 'Unauthorized - Admin access required' }, { status: 403 });
    }
    
    const supabase = await createServerSupabaseClient();
    const { id } = await context.params;
    
    if (!id) {
      return NextResponse.json({ error: 'Configuration ID is required' }, { status: 400 });
    }
    
    // Parse and validate request body
    const body = await request.json();
    const validationResult = updateSyncConfigSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: validationResult.error.issues
        },
        { status: 400 }
      );
    }
    
    const updateData = validationResult.data;
    
    // If updating spreadsheet_id or tab_name, check for duplicates
    if (updateData.spreadsheet_id || updateData.tab_name) {
      // Get current config to check what we're updating
      const { data: currentConfig, error: currentError } = await supabase
        .from('spreadsheet_sync_configs')
        .select('spreadsheet_id, tab_name')
        .eq('id', id)
        .single();
      
      if (currentError) {
        if (currentError.code === 'PGRST116') {
          return NextResponse.json({ error: 'Configuration not found' }, { status: 404 });
        }
        return NextResponse.json({ error: 'Failed to fetch current configuration' }, { status: 500 });
      }
      
      const newSpreadsheetId = updateData.spreadsheet_id || currentConfig.spreadsheet_id;
      const newTabName = updateData.tab_name || currentConfig.tab_name;
      
      // Check if this combination already exists (excluding current config)
      const { data: existing } = await supabase
        .from('spreadsheet_sync_configs')
        .select('id')
        .eq('spreadsheet_id', newSpreadsheetId)
        .eq('tab_name', newTabName)
        .neq('id', id)
        .single();
      
      if (existing) {
        return NextResponse.json(
          { error: 'Configuration already exists for this spreadsheet and tab combination' },
          { status: 409 }
        );
      }
    }
    
    // Update the configuration
    const { data, error } = await supabase
      .from('spreadsheet_sync_configs')
      .update({
        ...updateData,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();
    
    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Configuration not found' }, { status: 404 });
      }
      console.error('Failed to update sync configuration:', error);
      return NextResponse.json(
        { error: 'Failed to update sync configuration' },
        { status: 500 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Sync config PUT error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE - Delete sync configuration
export async function DELETE(
  request: NextRequest,
  context: RouteContext
) {
  try {
    // Check if user is admin
    const adminCheck = await isUserAdmin();
    if (!adminCheck) {
      return NextResponse.json({ error: 'Unauthorized - Admin access required' }, { status: 403 });
    }
    
    const supabase = await createServerSupabaseClient();
    const { id } = await context.params;
    
    if (!id) {
      return NextResponse.json({ error: 'Configuration ID is required' }, { status: 400 });
    }
    
    // Get the config before deleting to return it in response
    const { data: configToDelete, error: fetchError } = await supabase
      .from('spreadsheet_sync_configs')
      .select('id, name, spreadsheet_id, tab_name')
      .eq('id', id)
      .single();
    
    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Configuration not found' }, { status: 404 });
      }
      console.error('Failed to fetch configuration for deletion:', fetchError);
      return NextResponse.json(
        { error: 'Failed to fetch configuration' },
        { status: 500 }
      );
    }
    
    // Delete the configuration (sync_logs will be cascade deleted)
    const { error } = await supabase
      .from('spreadsheet_sync_configs')
      .delete()
      .eq('id', id);
    
    if (error) {
      console.error('Failed to delete sync configuration:', error);
      return NextResponse.json(
        { error: 'Failed to delete sync configuration' },
        { status: 500 }
      );
    }
    
    return NextResponse.json({
      success: true,
      message: 'Configuration deleted successfully',
      data: configToDelete,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Sync config DELETE error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}