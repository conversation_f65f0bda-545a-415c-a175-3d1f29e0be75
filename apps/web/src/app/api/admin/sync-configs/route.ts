import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '../../../../lib/server/supabase';
import { z } from 'zod';

// Validation schemas
const createSyncConfigSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name must be less than 100 characters'),
  spreadsheet_id: z.string().min(1, 'Spreadsheet ID is required'),
  tab_name: z.string().min(1, 'Tab name is required'),
  sync_order: z.number().int().min(0).default(0),
  is_active: z.boolean().default(true),
});

const updateSyncConfigSchema = createSyncConfigSchema.partial();

// Helper function to check if user is admin
async function isUserAdmin(): Promise<boolean> {
  try {
    const supabase = await createServerSupabaseClient();
    
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return false;
    }
    
    // Check if user has admin role
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('role')
      .eq('user_id', user.id)
      .single();
    
    if (profileError || !profile) {
      return false;
    }
    
    return profile.role === 'admin';
  } catch (error) {
    console.error('Error checking admin status:', error);
    return false;
  }
}

// GET - List all sync configurations
export async function GET(request: NextRequest) {
  try {
    // Check if user is admin
    const adminCheck = await isUserAdmin();
    if (!adminCheck) {
      return NextResponse.json({ error: 'Unauthorized - Admin access required' }, { status: 403 });
    }
    
    const supabase = await createServerSupabaseClient();
    
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const includeInactive = searchParams.get('include_inactive') === 'true';
    
    let query = supabase
      .from('spreadsheet_sync_configs')
      .select(`
        id,
        name,
        spreadsheet_id,
        tab_name,
        is_active,
        last_sync_at,
        last_sync_status,
        last_sync_details,
        sync_order,
        created_at,
        updated_at,
        created_by
      `)
      .order('sync_order', { ascending: true })
      .order('name', { ascending: true });
    
    if (!includeInactive) {
      query = query.eq('is_active', true);
    }
    
    const { data, error } = await query;
    
    if (error) {
      console.error('Failed to fetch sync configurations:', error);
      return NextResponse.json(
        { error: 'Failed to fetch sync configurations' },
        { status: 500 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: data || [],
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Sync configs GET error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST - Create new sync configuration
export async function POST(request: NextRequest) {
  try {
    // Check if user is admin
    const adminCheck = await isUserAdmin();
    if (!adminCheck) {
      return NextResponse.json({ error: 'Unauthorized - Admin access required' }, { status: 403 });
    }
    
    const supabase = await createServerSupabaseClient();
    
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }
    
    // Parse and validate request body
    const body = await request.json();
    const validationResult = createSyncConfigSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: validationResult.error.issues
        },
        { status: 400 }
      );
    }
    
    const configData = validationResult.data;
    
    // Check for duplicate configuration (same spreadsheet_id and tab_name)
    const { data: existing } = await supabase
      .from('spreadsheet_sync_configs')
      .select('id')
      .eq('spreadsheet_id', configData.spreadsheet_id)
      .eq('tab_name', configData.tab_name)
      .single();
    
    if (existing) {
      return NextResponse.json(
        { error: 'Configuration already exists for this spreadsheet and tab combination' },
        { status: 409 }
      );
    }
    
    // Create the configuration
    const { data, error } = await supabase
      .from('spreadsheet_sync_configs')
      .insert([{
        ...configData,
        created_by: user.id
      }])
      .select()
      .single();
    
    if (error) {
      console.error('Failed to create sync configuration:', error);
      return NextResponse.json(
        { error: 'Failed to create sync configuration' },
        { status: 500 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data,
      timestamp: new Date().toISOString()
    }, { status: 201 });
    
  } catch (error) {
    console.error('Sync configs POST error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}