import { NextRequest, NextResponse } from 'next/server';
import { sheetsSyncService } from '../../../../lib/services/sheets-sync';
import { createServerSupabaseClient } from '../../../../lib/server/supabase';

// Helper function to check if user is admin
async function isUserAdmin(): Promise<boolean> {
  try {
    const supabase = await createServerSupabaseClient();
    
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return false;
    }
    
    // Check if user has admin role
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('role')
      .eq('user_id', user.id)
      .single();
    
    if (profileError || !profile) {
      return false;
    }
    
    return profile.role === 'admin';
  } catch (error) {
    console.error('Error checking admin status:', error);
    return false;
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check if user is admin
    const adminCheck = await isUserAdmin();
    if (!adminCheck) {
      return NextResponse.json({ error: 'Unauthorized - Admin access required' }, { status: 403 });
    }
    
    // Parse request body for options
    const body = await request.json().catch(() => ({}));
    const { 
      dryRun = false, 
      useGoogleSheets = null, 
      csvFilePath = null,
      googleSheetsId = null,
      googleSheetsTab = null 
    } = body;
    
    console.log('🔄 Starting manual sheets sync...', { dryRun, useGoogleSheets });
    const startTime = Date.now();
    
    // Determine sync method
    const shouldUseGoogleSheets = useGoogleSheets ?? !!(process.env.GOOGLE_SHEETS_ID && process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL);
    const finalCsvPath = csvFilePath || process.env.CSV_FILE_PATH;
    
    if (!shouldUseGoogleSheets && !finalCsvPath) {
      return NextResponse.json({ 
        error: 'No data source configured. Provide googleSheetsId or csvFilePath, or set environment variables' 
      }, { status: 400 });
    }
    
    const result = await sheetsSyncService.sync({
      useGoogleSheets: shouldUseGoogleSheets,
      csvFilePath: finalCsvPath,
      googleSheetsId: googleSheetsId || process.env.GOOGLE_SHEETS_ID,
      googleSheetsTab: googleSheetsTab || process.env.GOOGLE_SHEETS_TAB,
      dryRun
    });
    
    const duration = Date.now() - startTime;
    
    console.log(`✅ Manual sync completed in ${duration}ms:`, result);
    
    // Return detailed sync results
    return NextResponse.json({
      success: true,
      duration,
      timestamp: new Date().toISOString(),
      dryRun,
      dataSource: shouldUseGoogleSheets ? 'Google Sheets' : 'CSV File',
      result: {
        totalProcessed: result.totalProcessed,
        created: result.created,
        updated: result.updated,
        skipped: result.skipped,
        errorCount: result.errors.length,
        errors: result.errors.slice(0, 10) // Limit errors in response
      }
    });
    
  } catch (error) {
    console.error('❌ Manual sync failed:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// GET endpoint for checking sync status or getting last sync info
export async function GET(request: NextRequest) {
  try {
    // Check if user is admin
    const adminCheck = await isUserAdmin();
    if (!adminCheck) {
      return NextResponse.json({ error: 'Unauthorized - Admin access required' }, { status: 403 });
    }
    
    // Return sync configuration info
    const hasGoogleSheets = !!(process.env.GOOGLE_SHEETS_ID && process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL);
    const hasCsvFile = !!process.env.CSV_FILE_PATH;
    
    return NextResponse.json({
      status: 'ready',
      timestamp: new Date().toISOString(),
      configuration: {
        hasGoogleSheets,
        hasCsvFile,
        preferredSource: hasGoogleSheets ? 'Google Sheets' : hasCsvFile ? 'CSV File' : 'None'
      }
    });
    
  } catch (error) {
    console.error('❌ Sync status check failed:', error);
    
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}