import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '../../../../lib/server/supabase';
import { multiSheetSyncService } from '../../../../lib/services/multi-sheet-sync';

// Helper function to check if user is admin
async function isUserAdmin(): Promise<boolean> {
  try {
    const supabase = await createServerSupabaseClient();
    
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return false;
    }
    
    // Check if user has admin role
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('role')
      .eq('user_id', user.id)
      .single();
    
    if (profileError || !profile) {
      return false;
    }
    
    return profile.role === 'admin';
  } catch (error) {
    console.error('Error checking admin status:', error);
    return false;
  }
}

// GET - Get sync logs with filtering options
export async function GET(request: NextRequest) {
  try {
    // Check if user is admin
    const adminCheck = await isUserAdmin();
    if (!adminCheck) {
      return NextResponse.json({ error: 'Unauthorized - Admin access required' }, { status: 403 });
    }
    
    const supabase = await createServerSupabaseClient();
    const { searchParams } = new URL(request.url);
    
    // Parse query parameters
    const configId = searchParams.get('config_id');
    const status = searchParams.get('status');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    const days = parseInt(searchParams.get('days') || '30');
    
    // Calculate date filter
    const since = new Date(Date.now() - (days * 24 * 60 * 60 * 1000)).toISOString();
    
    // Build query
    let query = supabase
      .from('sync_logs')
      .select(`
        id,
        config_id,
        started_at,
        completed_at,
        status,
        records_created,
        records_updated,
        records_skipped,
        error_count,
        errors,
        details,
        duration_ms,
        created_at,
        spreadsheet_sync_configs (
          id,
          name,
          spreadsheet_id,
          tab_name
        )
      `)
      .gte('started_at', since)
      .order('started_at', { ascending: false });
    
    // Apply filters
    if (configId) {
      query = query.eq('config_id', configId);
    }
    
    if (status && ['running', 'completed', 'failed', 'cancelled'].includes(status)) {
      query = query.eq('status', status);
    }
    
    // Apply pagination
    query = query.range(offset, offset + limit - 1);
    
    const { data, error, count } = await query;
    
    if (error) {
      console.error('Failed to fetch sync logs:', error);
      return NextResponse.json(
        { error: 'Failed to fetch sync logs' },
        { status: 500 }
      );
    }
    
    // Get total count for pagination
    let totalQuery = supabase
      .from('sync_logs')
      .select('id', { count: 'exact' })
      .gte('started_at', since);
    
    if (configId) {
      totalQuery = totalQuery.eq('config_id', configId);
    }
    
    if (status) {
      totalQuery = totalQuery.eq('status', status);
    }
    
    const { count: totalCount, error: countError } = await totalQuery;
    
    if (countError) {
      console.warn('Failed to get total count, using data length:', countError);
    }
    
    return NextResponse.json({
      success: true,
      data: data || [],
      pagination: {
        limit,
        offset,
        total: totalCount || data?.length || 0,
        hasMore: (data?.length || 0) === limit
      },
      filters: {
        config_id: configId,
        status,
        days
      },
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Sync logs GET error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST - Get sync statistics
export async function POST(request: NextRequest) {
  try {
    // Check if user is admin
    const adminCheck = await isUserAdmin();
    if (!adminCheck) {
      return NextResponse.json({ error: 'Unauthorized - Admin access required' }, { status: 403 });
    }
    
    const body = await request.json().catch(() => ({}));
    const { days = 7 } = body;
    
    try {
      const stats = await multiSheetSyncService.getSyncStats(days);
      
      return NextResponse.json({
        success: true,
        data: stats,
        period: {
          days,
          from: new Date(Date.now() - (days * 24 * 60 * 60 * 1000)).toISOString(),
          to: new Date().toISOString()
        },
        timestamp: new Date().toISOString()
      });
      
    } catch (error) {
      console.error('Failed to get sync stats:', error);
      return NextResponse.json(
        { error: 'Failed to get sync statistics' },
        { status: 500 }
      );
    }
    
  } catch (error) {
    console.error('Sync stats POST error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}