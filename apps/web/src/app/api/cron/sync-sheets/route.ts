import { NextRequest, NextResponse } from 'next/server';
import { sheetsSyncService } from '../../../../lib/services/sheets-sync';
import { multiSheetSyncService } from '../../../../lib/services/multi-sheet-sync';

export async function POST(request: NextRequest) {
  try {
    // Log cron request details for debugging
    const pathname = request.nextUrl?.pathname || 'unknown';
    const url = request.url;
    console.log('🕒 [CRON] Cron sync request received:', { pathname, url });
    
    // Verify this is a legitimate Vercel cron request
    const authHeader = request.headers.get('authorization');
    const cronSecret = process.env.CRON_SECRET;
    console.log('🕒 [CRON] Authorization header present:', <PERSON><PERSON><PERSON>(authHeader));
    console.log('🕒 [CRON] CRON_SECRET configured:', Boolean(cronSecret));

    if (!cronSecret) {
      console.error('CRON_SECRET not configured');
      return NextResponse.json({ error: 'Cron secret not configured' }, { status: 500 });
    }
    
    if (authHeader !== `Bearer ${cronSecret}`) {
      console.error('🚫 [CRON] Unauthorized cron request:', { 
        authHeaderProvided: Boolean(authHeader),
        authHeaderLength: authHeader?.length || 0,
        expectedFormat: 'Bearer [CRON_SECRET]'
      });
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    console.log('🔄 Starting scheduled multi-sheet sync...');
    const startTime = Date.now();
    
    try {
      // First try to use the new multi-sheet sync service
      const multiResult = await multiSheetSyncService.syncAll({
        maxConcurrent: 3,
        continueOnError: true
      });
      
      // If no configs were found, fall back to legacy single-sheet sync
      if (multiResult.totalConfigs === 0) {
        console.log('📋 No database configs found, falling back to legacy single-sheet sync...');
        
        // Determine sync method based on environment variables
        const useGoogleSheets = !!(process.env.GOOGLE_SHEETS_ID && process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL);
        const csvFilePath = process.env.CSV_FILE_PATH;
        
        if (!useGoogleSheets && !csvFilePath) {
          console.warn('⚠️ No database configs and no legacy environment variables configured');
          return NextResponse.json({ 
            success: true,
            message: 'No sync configurations found. Please add configurations via the admin panel.',
            timestamp: new Date().toISOString(),
            result: {
              totalConfigs: 0,
              successfulSyncs: 0,
              failedSyncs: 0,
              skippedSyncs: 0,
              duration: Date.now() - startTime
            }
          });
        }
        
        const legacyResult = await sheetsSyncService.sync({
          useGoogleSheets,
          csvFilePath,
          googleSheetsId: process.env.GOOGLE_SHEETS_ID,
          googleSheetsTab: process.env.GOOGLE_SHEETS_TAB,
          dryRun: false
        });
        
        const duration = Date.now() - startTime;
        
        console.log(`✅ Legacy sync completed in ${duration}ms:`, legacyResult);
        
        return NextResponse.json({
          success: true,
          duration,
          timestamp: new Date().toISOString(),
          legacyMode: true,
          result: {
            totalProcessed: legacyResult.totalProcessed,
            created: legacyResult.created,
            updated: legacyResult.updated,
            skipped: legacyResult.skipped,
            errorCount: legacyResult.errors.length,
            errors: legacyResult.errors
          }
        });
      }
      
      const duration = Date.now() - startTime;
      
      console.log(`✅ Multi-sheet sync completed in ${duration}ms:`, multiResult);
      
      // Return detailed multi-sync results
      return NextResponse.json({
        success: true,
        duration,
        timestamp: new Date().toISOString(),
        multiSheetMode: true,
        result: {
          totalConfigs: multiResult.totalConfigs,
          successfulSyncs: multiResult.successfulSyncs,
          failedSyncs: multiResult.failedSyncs,
          skippedSyncs: multiResult.skippedSyncs,
          configResults: multiResult.results.map(r => ({
            name: r.configName,
            status: r.status,
            error: r.error,
            duration: r.duration,
            records: r.result ? {
              created: r.result.created,
              updated: r.result.updated,
              skipped: r.result.skipped,
              errors: r.result.errors.length
            } : null
          }))
        }
      });
      
    } catch (error) {
      console.error('❌ Multi-sheet sync failed, attempting legacy fallback:', error);
      
      // Fallback to legacy sync if multi-sheet sync fails
      try {
        const useGoogleSheets = !!(process.env.GOOGLE_SHEETS_ID && process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL);
        const csvFilePath = process.env.CSV_FILE_PATH;
        
        if (useGoogleSheets || csvFilePath) {
          console.log('🔄 Attempting legacy fallback sync...');
          
          const legacyResult = await sheetsSyncService.sync({
            useGoogleSheets,
            csvFilePath,
            googleSheetsId: process.env.GOOGLE_SHEETS_ID,
            googleSheetsTab: process.env.GOOGLE_SHEETS_TAB,
            dryRun: false
          });
          
          const duration = Date.now() - startTime;
          
          console.log(`✅ Legacy fallback sync completed in ${duration}ms:`, legacyResult);
          
          return NextResponse.json({
            success: true,
            duration,
            timestamp: new Date().toISOString(),
            fallbackMode: true,
            warning: 'Multi-sheet sync failed, used legacy mode',
            result: {
              totalProcessed: legacyResult.totalProcessed,
              created: legacyResult.created,
              updated: legacyResult.updated,
              skipped: legacyResult.skipped,
              errorCount: legacyResult.errors.length,
              errors: legacyResult.errors
            }
          });
        }
      } catch (fallbackError) {
        console.error('❌ Legacy fallback also failed:', fallbackError);
      }
      
      throw error; // Re-throw the original error if fallback also failed
    }
    
  } catch (error) {
    console.error('❌ Cron sync failed:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// Ensure only POST method is allowed
export async function GET(request: NextRequest) {
  // Allow GET for Vercel Cron; reuse POST handler
  console.log('🕒 [CRON] GET /api/cron/sync-sheets received, delegating to POST handler');
  return POST(request);
}