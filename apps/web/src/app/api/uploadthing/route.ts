import { createRouteHand<PERSON> } from "uploadthing/next";

import { ourFileRouter } from "@/lib/uploadthing/core";

// Export routes for Next App Router
export const { GET, POST } = createRouteHandler({
  router: ourFileRouter,
  
  // Apply custom config if needed
  config: {
    // Optional: Configure upload settings
    // callbackUrl: `${process.env.NEXT_PUBLIC_SERVER_URL}/api/uploadthing`,
  },
});