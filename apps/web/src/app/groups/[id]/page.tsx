"use client";

import { useState, use } from "react";
import { useRouter } from "next/navigation";
import { trpc } from "@/utils/trpc";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import Loader from "@/components/loader";
import { ArrowLeft, Edit, Trash2, ExternalLink, Calendar, TrendingUp, ChevronRight, Share2, Copy, Globe, Lock } from "lucide-react";
import { EngagementMetrics } from "@/components/ui/engagement-metrics";
import { ContentPreview } from "@/components/ui/content-preview";
import { PlatformIcon } from "@/components/ui/platform-icon";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";

interface GroupPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function GroupPage({ params }: GroupPageProps) {
  const resolvedParams = use(params);
  const router = useRouter();
  const [isEditing, setIsEditing] = useState(false);
  const [editName, setEditName] = useState("");
  const [editDescription, setEditDescription] = useState("");
  const [editIsPublic, setEditIsPublic] = useState(true);

  const utils = trpc.useUtils();
  
  const { data: group, isLoading } = trpc.groups.getGroup.useQuery({ id: resolvedParams.id });

  console.log("Group detail data:", group);

  const updateGroupMutation = trpc.groups.updateGroup.useMutation({
    onSuccess: () => {
      console.log("Group updated successfully");
      toast.success("Group updated successfully!");
      setIsEditing(false);
      utils.groups.getGroup.invalidate({ id: resolvedParams.id });
    },
    onError: (error) => {
      console.error("Error updating group:", error);
      toast.error(error.message);
    },
  });

  const removeContentMutation = trpc.groups.removeContentFromGroup.useMutation({
    onSuccess: () => {
      toast.success("Content removed from group!");
      utils.groups.getGroup.invalidate({ id: resolvedParams.id });
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const handleUpdateGroup = () => {
    if (!editName.trim()) {
      toast.error("Group name is required");
      return;
    }

    updateGroupMutation.mutate({
      id: resolvedParams.id,
      name: editName.trim(),
      description: editDescription.trim() || undefined,
      is_public: editIsPublic,
    });
  };

  const handleShareGroup = () => {
    if (!group?.is_public) {
      toast.error("Only public groups can be shared");
      return;
    }

    const shareUrl = `${window.location.origin}/groups/share/${group.id}`;
    navigator.clipboard.writeText(shareUrl);
    toast.success("Share link copied to clipboard!");
  };

  const handleRemoveContent = (contentId: number, contentTitle: string) => {
    if (confirm(`Remove "${contentTitle || 'this content'}" from the group?`)) {
      removeContentMutation.mutate({
        group_id: resolvedParams.id,
        content_id: contentId,
      });
    }
  };

  const startEditing = () => {
    if (group) {
      setEditName(group.name);
      setEditDescription(group.description || "");
      setEditIsPublic(group.is_public);
      setIsEditing(true);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader />
      </div>
    );
  }

  if (!group) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
            Group not found
          </h1>
          <Button onClick={() => router.push("/groups")}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Groups
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6 sm:mb-8">
        <Button
          variant="ghost"
          onClick={() => router.push("/groups")}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          <span className="hidden sm:inline">Back to Groups</span>
          <span className="sm:hidden">Back</span>
        </Button>
      </div>

      {/* Group info */}
      <div className="mb-6 sm:mb-8">
        {isEditing ? (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg sm:text-xl">Edit Group</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="edit-name" className="text-sm font-medium">Group Name</Label>
                <Input
                  id="edit-name"
                  value={editName}
                  onChange={(e) => setEditName(e.target.value)}
                  maxLength={100}
                  className="w-full"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-description" className="text-sm font-medium">Description</Label>
                <Input
                  id="edit-description"
                  value={editDescription}
                  onChange={(e) => setEditDescription(e.target.value)}
                  maxLength={500}
                  placeholder="Optional description"
                  className="w-full"
                />
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">Privacy Settings</Label>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="edit-group-public"
                    checked={editIsPublic}
                    onCheckedChange={(checked) => setEditIsPublic(checked === true)}
                  />
                  <Label htmlFor="edit-group-public" className="text-sm flex items-center gap-2 cursor-pointer">
                    {editIsPublic ? (
                      <>
                        <Globe className="h-4 w-4 text-green-600" />
                        <span>Make this group public (anyone can view when shared)</span>
                      </>
                    ) : (
                      <>
                        <Lock className="h-4 w-4 text-gray-600" />
                        <span>Keep this group private (only you can view)</span>
                      </>
                    )}
                  </Label>
                </div>
                <p className="text-xs text-gray-500 ml-6">
                  {editIsPublic ? 
                    "Public groups can be shared with others via a link" : 
                    "Private groups are only visible to you"
                  }
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-2">
                <Button
                  onClick={handleUpdateGroup}
                  disabled={updateGroupMutation.isPending}
                  className="w-full sm:w-auto"
                >
                  {updateGroupMutation.isPending ? "Saving..." : "Save Changes"}
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setIsEditing(false)}
                  className="w-full sm:w-auto"
                >
                  Cancel
                </Button>
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/50 dark:to-indigo-950/50 rounded-xl p-4 sm:p-6 lg:p-8 border border-blue-100 dark:border-blue-900">
            <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6">
              <div className="min-w-0 flex-1">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-10 h-10 sm:w-12 sm:h-12 bg-blue-600 rounded-lg flex items-center justify-center">
                    <span className="text-white text-lg sm:text-xl font-bold">{group.name.charAt(0).toUpperCase()}</span>
                  </div>
                  <div>
                    <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white mb-1">
                      {group.name}
                    </h1>
                    <div className="flex items-center gap-3 text-sm text-gray-600 dark:text-gray-400">
                      <Badge variant="secondary" className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                        {group.content_count} {group.content_count === 1 ? 'item' : 'items'}
                      </Badge>
                      <Badge 
                        variant="secondary" 
                        className={group.is_public ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200" : "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"}
                      >
                        {group.is_public ? (
                          <><Globe className="h-3 w-3 mr-1" />Public</>
                        ) : (
                          <><Lock className="h-3 w-3 mr-1" />Private</>
                        )}
                      </Badge>
                      <span>Created {new Date(group.created_at).toLocaleDateString('en-US', { 
                        year: 'numeric', 
                        month: 'long', 
                        day: 'numeric' 
                      })}</span>
                    </div>
                  </div>
                </div>
                
                {group.description && (
                  <div className="bg-white/60 dark:bg-gray-800/60 rounded-lg p-4 backdrop-blur-sm">
                    <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                      {group.description}
                    </p>
                  </div>
                )}
              </div>

              <div className="flex flex-col sm:flex-row gap-2">
                {group.is_public && (
                  <Button
                    variant="outline"
                    onClick={handleShareGroup}
                    className="flex items-center gap-2 bg-white hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 border-gray-200 dark:border-gray-700 px-6 py-2.5"
                  >
                    <Share2 className="h-4 w-4" />
                    <span>Share Group</span>
                  </Button>
                )}
                <Button
                  variant="outline"
                  onClick={startEditing}
                  className="flex items-center gap-2 bg-white hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 border-gray-200 dark:border-gray-700 px-6 py-2.5"
                >
                  <Edit className="h-4 w-4" />
                  <span>Edit Group</span>
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Content Section */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Content Collection
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              {group.content?.length || 0} {(group.content?.length || 0) === 1 ? 'item' : 'items'} in this group
            </p>
          </div>
        </div>
      </div>

      {/* Content grid */}
      {group.content && group.content.length > 0 ? (
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4 lg:gap-6">
          {group.content.map((content) => {
            // Determine platform from content or default to twitter
            const platform = content.content_link?.includes('twitter.com') || content.content_link?.includes('x.com') ? 'twitter' :
                            content.content_link?.includes('linkedin.com') ? 'linkedin' :
                            content.content_link?.includes('youtube.com') ? 'youtube' : 'other';
            
            return (
              <Card key={content.id} className="group relative overflow-hidden hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-l-4 border-l-blue-500">
                {/* Header Section */}
                <CardHeader className="pb-4">
                  <div className="flex items-start justify-between gap-3">
                    <div className="flex items-start gap-3 flex-1 min-w-0">
                      <div className="flex-shrink-0 mt-1">
                        <PlatformIcon platform={platform} size="md" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <CardTitle className="text-lg font-semibold line-clamp-2 text-gray-900 dark:text-white mb-2">
                          {content.content_title || `Content from @${content.host}`}
                        </CardTitle>
                        <div className="flex items-center gap-2 text-sm text-gray-500">
                          <span className="font-medium">@{content.host}</span>
                          {content.content_account && content.content_account.length > 1 && (
                            <span className="text-xs bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
                              +{content.content_account.length - 1} more
                            </span>
                          )}
                          <span className="text-gray-300">•</span>
                          <span>{new Date(content.content_created_date).toLocaleDateString()}</span>
                        </div>
                      </div>
                    </div>
                    
                    {/* Remove button */}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveContent(content.id, content.content_title || "")}
                      className="text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950 opacity-0 group-hover:opacity-100 transition-all duration-200 absolute top-3 right-3"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </CardHeader>

                <CardContent className="space-y-4">
                  {/* Content Preview */}
                  {content.content_description && (
                    <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-3">
                      <p className="text-sm text-gray-700 dark:text-gray-300 line-clamp-3">
                        {content.content_description}
                      </p>
                    </div>
                  )}

                  {/* Engagement Metrics */}
                  {(content.content_views > 0 || content.content_listeners > 0 || content.twitter_impressions > 0 || content.content_follow_increase > 0) && (
                    <EngagementMetrics
                      views={content.content_views}
                      listeners={content.content_listeners}
                      impressions={content.twitter_impressions}
                      followIncrease={content.content_follow_increase}
                      variant="compact"
                    />
                  )}

                  {/* Tags */}
                  {content.content_tags && content.content_tags.length > 0 && (() => {
                    const filteredTags = content.content_tags.filter(tag => !tag.includes('follow_increase'));
                    return filteredTags.length > 0 && (
                      <div className="flex flex-wrap gap-1.5">
                        {filteredTags.slice(0, 4).map((tag) => (
                          <Badge
                            key={tag}
                            variant="secondary"
                            className="text-xs px-2 py-1 bg-blue-50 text-blue-700 hover:bg-blue-100 dark:bg-blue-900/50 dark:text-blue-300"
                          >
                            {tag}
                          </Badge>
                        ))}
                        {filteredTags.length > 4 && (
                          <Badge variant="outline" className="text-xs px-2 py-1">
                            +{filteredTags.length - 4}
                          </Badge>
                        )}
                      </div>
                    );
                  })()}

                  {/* Action Section */}
                  <div className="flex items-center justify-between pt-2 border-t border-gray-100 dark:border-gray-800">
                    <div className="text-xs text-gray-500">
                      Added {new Date(content.added_at).toLocaleDateString()}
                    </div>
                    
                    <Button
                      variant="default"
                      size="sm"
                      onClick={() => window.open(content.content_link, "_blank")}
                      className="bg-blue-600 hover:bg-blue-700 text-white transition-colors group/button"
                    >
                      <span className="text-sm font-medium">View Content</span>
                      <ChevronRight className="h-3 w-3 ml-1 group-hover/button:translate-x-0.5 transition-transform" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      ) : (
        <Card className="text-center py-16 border-dashed border-2 border-gray-200 dark:border-gray-700 bg-gray-50/50 dark:bg-gray-800/50">
          <CardContent>
            <div className="mx-auto w-16 h-16 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center mb-6">
              <div className="text-2xl">📋</div>
            </div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
              No content in this group yet
            </h3>
            <p className="text-gray-600 dark:text-gray-400 max-w-md mx-auto mb-6">
              Start building your collection by adding content from around the dashboard. Use the pin button on any content piece to save it here.
            </p>
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-lg text-sm font-medium">
              💡 Tip: Look for the pin icon on content cards
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}