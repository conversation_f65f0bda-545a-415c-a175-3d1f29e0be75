"use client";

import { useState, useEffect, useCallback, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { trpc } from "@/utils/trpc";
import { SearchBar } from "@/components/search-bar";
import { ContentTable } from "@/components/content-table";
import { TestimonialsSection } from "@/components/testimonials-section";
import { Zap, Users, MessageCircle, TrendingUp } from "lucide-react";

interface Filters {
  contentTypes: string[];
  categories: string[];
  sortBy: string;
  sortOrder: string;
}

function HomeContent() {
  const router = useRouter();
  const searchParams = useSearchParams();

  console.log('Dashboard page loaded');
  
  // Initialize state from URL parameters
  const [searchQuery, setSearchQuery] = useState(() => searchParams.get("q") || "");
  const [filters, setFilters] = useState<Filters>(() => ({
    contentTypes: searchParams.get("types")?.split(",").filter(Boolean) || [],
    categories: searchParams.get("categories")?.split(",").filter(Boolean) || [],
    sortBy: searchParams.get("sort") || "impressions",
    sortOrder: searchParams.get("order") || "desc"
  }));

  // Pagination state for each section
  const [twitterPage, setTwitterPage] = useState(1);
  const [marketingPage, setMarketingPage] = useState(1);
  const [testimonialsPage, setTestimonialsPage] = useState(1);

  // Update URL when state changes
  const updateURL = useCallback((newQuery: string, newFilters: Filters) => {
    const params = new URLSearchParams();

    // Only add non-default values to keep URL clean
    if (newQuery) params.set("q", newQuery);
    if (newFilters.contentTypes.length > 0) params.set("types", newFilters.contentTypes.join(","));
    if (newFilters.categories.length > 0) params.set("categories", newFilters.categories.join(","));
    if (newFilters.sortBy !== "impressions") params.set("sort", newFilters.sortBy);
    if (newFilters.sortOrder !== "desc") params.set("order", newFilters.sortOrder);

    const queryString = params.toString();
    const newURL = queryString ? `/dashboard?${queryString}` : "/dashboard";

    console.log('Updating dashboard URL to:', newURL);

    // Update URL without refreshing the page
    router.replace(newURL, { scroll: false });
  }, [router]);

  // Sync URL when search query changes
  useEffect(() => {
    updateURL(searchQuery, filters);
  }, [searchQuery, filters, updateURL]);

  // Handle browser back/forward navigation
  useEffect(() => {
    const handlePopState = () => {
      // Use searchParams from Next.js instead of window.location
      const currentQuery = searchParams.get("q") || "";
      const currentFilters = {
        contentTypes: searchParams.get("types")?.split(",").filter(Boolean) || [],
        categories: searchParams.get("categories")?.split(",").filter(Boolean) || [],
        sortBy: searchParams.get("sort") || "impressions",
        sortOrder: searchParams.get("order") || "desc"
      };

      setSearchQuery(currentQuery);
      setFilters(currentFilters);
    };

    // Only add event listener on client side
    if (typeof window !== 'undefined') {
      window.addEventListener("popstate", handlePopState);
      return () => window.removeEventListener("popstate", handlePopState);
    }
  }, [searchParams]);

  // Queries for all sections with pagination
  const twitterContentQuery = trpc.content.public.getAllTwitterContent.useQuery({ 
    page: twitterPage, 
    limit: 9 
  });
  const marketingCaseStudiesQuery = trpc.content.public.getMarketingCaseStudies.useQuery({ 
    page: marketingPage, 
    limit: 9 
  });
  const testimonialsQuery = trpc.content.public.getTestimonials.useQuery({ 
    page: testimonialsPage, 
    limit: 9 
  });

  // Keep the filtered content query for search functionality
  const filteredContentQuery = trpc.content.public.getFilteredContent.useQuery({
    search: searchQuery,
    contentTypes: filters.contentTypes,
    categories: filters.categories,
    sortBy: filters.sortBy as "impressions" | "date" | "likes" | "retweets",
    sortOrder: filters.sortOrder as "desc" | "asc",
    limit: 30
  });

  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };

  const handleFilter = (filterType: string, values: string[]) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: values
    }));
  };

  const handleSort = (sortBy: string, sortOrder?: string) => {
    setFilters(prev => ({
      ...prev,
      sortBy,
      sortOrder: sortOrder || prev.sortOrder
    }));
  };

  const getDisplayTitle = () => {
    let title = "Filtered Content";
    
    if (filters.contentTypes.length > 0) {
      const typeLabels = filters.contentTypes.map(type => {
        const typeMap: Record<string, string> = {
          twitter: "Twitter",
          spaces: "Spaces",
          tweets: "Tweets",
          marketing: "Marketing",
          presskit: "Press Kit",
          incubation: "Incubation",
          testimonials: "Testimonials"
        };
        return typeMap[type] || type;
      });
      title = typeLabels.join(" • ");
    }
    
    if (filters.categories.length > 0) {
      const categoryLabels = filters.categories.map(cat => cat.toUpperCase());
      title += ` • ${categoryLabels.join(" • ")}`;
    }
    
    if (searchQuery) {
      title = `Search: "${searchQuery}"`;
    }
    
    return title;
  };

  const isSearchActive = searchQuery || filters.contentTypes.length > 0 || filters.categories.length > 0;

  return (
    <div className="min-h-screen gradient-tech">
      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/3 via-transparent to-accent/3" />
        <div className="relative content-container py-20 lg:py-32">
          {/* Main Hero Content */}
          <div className="center-all flex-col text-center space-y-12">
            {/* Title */}
            <div className="space-y-6">
              <h1 className="text-5xl sm:text-6xl lg:text-7xl xl:text-8xl font-bold tracking-tight leading-tight">
                <div className="bg-gradient-to-r from-foreground to-muted-foreground bg-clip-text text-transparent">
                  IBC VENTURES
                </div>
                <div className="bg-gradient-to-r from-ibc-green to-ibc-green-light bg-clip-text text-transparent mt-2">
                  PORTFOLIO
                </div>
              </h1>
              
              <p className="text-muted-foreground text-xl sm:text-2xl max-w-3xl mx-auto leading-relaxed font-medium">
                The ONLY media-led incubator and tokenized deal desk and venture fund in the world
              </p>
            </div>
            
            {/* Search Bar */}
            <div className="w-full max-w-5xl mx-auto flex flex-col items-center">
              <SearchBar
                onSearch={handleSearch}
                onFilter={handleFilter}
                onSort={handleSort}
                placeholder="Search across all content..."
                filters={filters}
                searchQuery={searchQuery}
              />
            </div>
          </div>
        </div>
      </section>

      {/* Content Sections */}
      <section className="content-container pb-20">
        {isSearchActive ? (
          // Show filtered results when search is active
          <div className="glass-effect rounded-3xl p-10 card-hover">
            <ContentTable
              title={getDisplayTitle()}
              data={filteredContentQuery.data || []}
              titleColor="chart-1"
              isLoading={filteredContentQuery.isLoading}
            />
          </div>
        ) : (
          // Show all sections when no search is active
          <div className="space-y-20">
            {/* Twitter Spaces & Tweets Section */}
            <div className="glass-effect rounded-3xl p-10 card-hover">
              <ContentTable
                title="Twitter Spaces & Tweets"
                data={twitterContentQuery.data?.data || []}
                titleColor="chart-1"
                isLoading={twitterContentQuery.isLoading}
                currentPage={twitterPage}
                totalItems={twitterContentQuery.data?.total || 0}
                onPageChange={setTwitterPage}
                itemsPerPage={9}
              />
            </div>

            {/* Marketing Case Studies Section */}
            <div className="glass-effect rounded-3xl p-10 card-hover">
              <ContentTable
                title="Marketing Case Studies"
                data={marketingCaseStudiesQuery.data?.data || []}
                titleColor="chart-2"
                isLoading={marketingCaseStudiesQuery.isLoading}
                currentPage={marketingPage}
                totalItems={marketingCaseStudiesQuery.data?.total || 0}
                onPageChange={setMarketingPage}
                itemsPerPage={9}
              />
            </div>
          </div>
        )}
      </section>

      {/* Full-Screen Testimonials Section - Outside any container */}
      {!isSearchActive && (
        <TestimonialsSection
          testimonials={testimonialsQuery.data?.data || []}
          isLoading={testimonialsQuery.isLoading}
          showViewAll={true}
        />
      )}
    </div>
  );
}

export default function Dashboard() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <HomeContent />
    </Suspense>
  );
}
