"use client";

import { useState, useEffect } from "react";
import { AdminGuard } from "@/components/admin/guards/AdminGuard";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import Loader from "@/components/loader";
import { toast } from "sonner";
import { 
  Plus, 
  Settings, 
  Play, 
  Pause, 
  Trash2, 
  Edit, 
  Clock, 
  CheckCircle, 
  XCircle,
  BarChart3,
  RefreshCw,
  ExternalLink,
  Database,
  History
} from "lucide-react";
import { ConfirmDialog } from "@/components/ui/confirm-dialog";
import { Textarea } from "@/components/ui/textarea";

interface SyncConfig {
  id: string;
  name: string;
  spreadsheet_id: string;
  tab_name: string;
  is_active: boolean;
  last_sync_at: string | null;
  last_sync_status: string | null;
  last_sync_details: any;
  sync_order: number;
  created_at: string;
  updated_at: string;
  created_by: string;
}

interface SyncLog {
  id: string;
  config_id: string;
  started_at: string;
  completed_at: string | null;
  status: 'running' | 'completed' | 'failed' | 'cancelled';
  records_created: number;
  records_updated: number;
  records_skipped: number;
  error_count: number;
  errors: string[] | null;
  details: any;
  duration_ms: number | null;
  created_at: string;
  spreadsheet_sync_configs?: {
    id: string;
    name: string;
    spreadsheet_id: string;
    tab_name: string;
  };
}

interface SyncStats {
  totalSyncs: number;
  successfulSyncs: number;
  failedSyncs: number;
  averageDuration: number;
  recentErrors: string[];
}

type FormData = Omit<SyncConfig, 'id' | 'created_at' | 'updated_at' | 'created_by' | 'last_sync_at' | 'last_sync_status' | 'last_sync_details'>;

export default function SyncConfigsPage() {
  const [configs, setConfigs] = useState<SyncConfig[]>([]);
  const [logs, setLogs] = useState<SyncLog[]>([]);
  const [stats, setStats] = useState<SyncStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [logsLoading, setLogsLoading] = useState(false);
  const [statsLoading, setStatsLoading] = useState(false);
  
  const [showForm, setShowForm] = useState(false);
  const [showLogs, setShowLogs] = useState(false);
  const [showStats, setShowStats] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  
  const [formData, setFormData] = useState<FormData>({
    name: '',
    spreadsheet_id: '',
    tab_name: 'Sheet1',
    is_active: true,
    sync_order: 0,
  });
  
  const [submitting, setSubmitting] = useState(false);
  const [syncing, setSyncing] = useState<string | null>(null);
  
  // Delete confirmation state
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [pendingDeleteId, setPendingDeleteId] = useState<string | null>(null);

  // Load sync configurations
  const loadConfigs = async () => {
    try {
      const response = await fetch('/api/admin/sync-configs');
      if (!response.ok) {
        throw new Error('Failed to fetch configurations');
      }
      const data = await response.json();
      setConfigs(data.data);
    } catch (error) {
      console.error('Failed to load configs:', error);
      toast.error('Failed to load sync configurations');
    } finally {
      setLoading(false);
    }
  };

  // Load sync logs
  const loadLogs = async () => {
    try {
      setLogsLoading(true);
      const response = await fetch('/api/admin/sync-logs?limit=20');
      if (!response.ok) {
        throw new Error('Failed to fetch logs');
      }
      const data = await response.json();
      setLogs(data.data);
    } catch (error) {
      console.error('Failed to load logs:', error);
      toast.error('Failed to load sync logs');
    } finally {
      setLogsLoading(false);
    }
  };

  // Load sync statistics
  const loadStats = async () => {
    try {
      setStatsLoading(true);
      const response = await fetch('/api/admin/sync-logs', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ days: 7 })
      });
      if (!response.ok) {
        throw new Error('Failed to fetch stats');
      }
      const data = await response.json();
      setStats(data.data);
    } catch (error) {
      console.error('Failed to load stats:', error);
      toast.error('Failed to load sync statistics');
    } finally {
      setStatsLoading(false);
    }
  };

  useEffect(() => {
    loadConfigs();
  }, []);

  useEffect(() => {
    if (showLogs && logs.length === 0) {
      loadLogs();
    }
  }, [showLogs]);

  useEffect(() => {
    if (showStats && !stats) {
      loadStats();
    }
  }, [showStats]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim() || !formData.spreadsheet_id.trim() || !formData.tab_name.trim()) {
      toast.error('Please fill in all required fields');
      return;
    }

    setSubmitting(true);
    
    try {
      const url = editingId 
        ? `/api/admin/sync-configs/${editingId}`
        : '/api/admin/sync-configs';
      
      const method = editingId ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Operation failed');
      }
      
      toast.success(editingId ? 'Configuration updated successfully' : 'Configuration created successfully');
      setShowForm(false);
      setEditingId(null);
      resetForm();
      loadConfigs();
      
    } catch (error) {
      console.error('Submit error:', error);
      toast.error(error instanceof Error ? error.message : 'Operation failed');
    } finally {
      setSubmitting(false);
    }
  };

  // Handle manual sync
  const handleSync = async (config: SyncConfig) => {
    setSyncing(config.id);
    
    try {
      const response = await fetch(`/api/admin/sync-configs/${config.id}/sync`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ dryRun: false })
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Sync failed');
      }
      
      const data = await response.json();
      
      if (data.success) {
        toast.success(`Sync completed for ${config.name}: ${data.result.created} created, ${data.result.updated} updated`);
        loadConfigs(); // Refresh to show updated sync status
        if (showLogs) loadLogs(); // Refresh logs if showing
      } else {
        throw new Error(data.error || 'Sync failed');
      }
      
    } catch (error) {
      console.error('Sync error:', error);
      toast.error(error instanceof Error ? error.message : 'Sync failed');
    } finally {
      setSyncing(null);
    }
  };

  // Handle edit
  const handleEdit = (config: SyncConfig) => {
    setFormData({
      name: config.name,
      spreadsheet_id: config.spreadsheet_id,
      tab_name: config.tab_name,
      is_active: config.is_active,
      sync_order: config.sync_order,
    });
    setEditingId(config.id);
    setShowForm(true);
  };

  // Handle delete
  const handleDelete = async () => {
    if (!pendingDeleteId) return;
    
    try {
      const response = await fetch(`/api/admin/sync-configs/${pendingDeleteId}`, {
        method: 'DELETE'
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Delete failed');
      }
      
      toast.success('Configuration deleted successfully');
      loadConfigs();
      
    } catch (error) {
      console.error('Delete error:', error);
      toast.error(error instanceof Error ? error.message : 'Delete failed');
    } finally {
      setDeleteDialogOpen(false);
      setPendingDeleteId(null);
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      spreadsheet_id: '',
      tab_name: 'Sheet1',
      is_active: true,
      sync_order: 0,
    });
  };

  const formatDuration = (ms: number | null) => {
    if (!ms) return 'N/A';
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}m`;
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleString();
  };

  const getStatusIcon = (status: string | null) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'running':
        return <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />;
      default:
        return <Clock className="h-4 w-4 text-muted-foreground" />;
    }
  };

  if (loading) {
    return (
      <AdminGuard>
        <div className="min-h-screen flex items-center justify-center">
          <Loader />
        </div>
      </AdminGuard>
    );
  }

  return (
    <AdminGuard>
      <div className="min-h-screen bg-background p-8">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold">Sync Configurations</h1>
              <p className="text-muted-foreground">
                Manage spreadsheet sync configurations and monitor sync operations
              </p>
            </div>
            <div className="flex gap-2">
              <Button
                onClick={() => setShowStats(!showStats)}
                variant="outline"
                className="flex items-center gap-2"
              >
                <BarChart3 className="h-4 w-4" />
                {showStats ? 'Hide Stats' : 'Show Stats'}
              </Button>
              <Button
                onClick={() => setShowLogs(!showLogs)}
                variant="outline"
                className="flex items-center gap-2"
              >
                <History className="h-4 w-4" />
                {showLogs ? 'Hide Logs' : 'Show Logs'}
              </Button>
              <Button
                onClick={() => {
                  setShowForm(true);
                  setEditingId(null);
                  resetForm();
                }}
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Add Configuration
              </Button>
            </div>
          </div>

          {/* Statistics */}
          {showStats && (
            <Card className="p-6 mb-8">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold">Sync Statistics (Last 7 Days)</h2>
                <Button
                  onClick={loadStats}
                  variant="outline"
                  size="sm"
                  disabled={statsLoading}
                  className="flex items-center gap-2"
                >
                  <RefreshCw className={`h-4 w-4 ${statsLoading ? 'animate-spin' : ''}`} />
                  Refresh
                </Button>
              </div>
              
              {statsLoading ? (
                <div className="flex justify-center py-8">
                  <Loader />
                </div>
              ) : stats ? (
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="text-center p-4 bg-muted rounded-lg">
                    <div className="text-2xl font-bold">{stats.totalSyncs}</div>
                    <div className="text-sm text-muted-foreground">Total Syncs</div>
                  </div>
                  <div className="text-center p-4 bg-green-50 dark:bg-green-950 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">{stats.successfulSyncs}</div>
                    <div className="text-sm text-muted-foreground">Successful</div>
                  </div>
                  <div className="text-center p-4 bg-red-50 dark:bg-red-950 rounded-lg">
                    <div className="text-2xl font-bold text-red-600">{stats.failedSyncs}</div>
                    <div className="text-sm text-muted-foreground">Failed</div>
                  </div>
                  <div className="text-center p-4 bg-blue-50 dark:bg-blue-950 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">{formatDuration(stats.averageDuration)}</div>
                    <div className="text-sm text-muted-foreground">Avg Duration</div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  No statistics available
                </div>
              )}
            </Card>
          )}

          {/* Configuration Form */}
          {showForm && (
            <Card className="p-6 mb-8">
              <h2 className="text-xl font-semibold mb-4">
                {editingId ? 'Edit Configuration' : 'Add New Configuration'}
              </h2>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <Label htmlFor="name">Configuration Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="e.g., Main Dashboard Data"
                    required
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="spreadsheet_id">Google Sheets ID *</Label>
                    <Input
                      id="spreadsheet_id"
                      value={formData.spreadsheet_id}
                      onChange={(e) => setFormData(prev => ({ ...prev, spreadsheet_id: e.target.value }))}
                      placeholder="1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"
                      required
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="tab_name">Tab Name *</Label>
                    <Input
                      id="tab_name"
                      value={formData.tab_name}
                      onChange={(e) => setFormData(prev => ({ ...prev, tab_name: e.target.value }))}
                      placeholder="Sheet1"
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="sync_order">Sync Order (0 = first)</Label>
                    <Input
                      id="sync_order"
                      type="number"
                      min="0"
                      value={formData.sync_order}
                      onChange={(e) => setFormData(prev => ({ ...prev, sync_order: parseInt(e.target.value) || 0 }))}
                    />
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="is_active"
                      checked={formData.is_active}
                      onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_active: checked }))}
                    />
                    <Label htmlFor="is_active">Active</Label>
                  </div>
                </div>

                <div className="flex gap-2 pt-4">
                  <Button type="submit" disabled={submitting}>
                    {submitting ? 'Saving...' : editingId ? 'Update' : 'Create'}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setShowForm(false);
                      setEditingId(null);
                      resetForm();
                    }}
                  >
                    Cancel
                  </Button>
                </div>
              </form>
            </Card>
          )}

          {/* Configurations List */}
          <Card className="p-6 mb-8">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold">Sync Configurations</h2>
              <Button
                onClick={loadConfigs}
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                Refresh
              </Button>
            </div>

            {configs.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No configurations found. Create your first sync configuration!
              </div>
            ) : (
              <div className="space-y-4">
                {configs.map((config) => (
                  <div
                    key={config.id}
                    className="p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="font-semibold">{config.name}</h3>
                          <Badge variant={config.is_active ? 'default' : 'secondary'}>
                            {config.is_active ? 'Active' : 'Inactive'}
                          </Badge>
                          {config.last_sync_status && getStatusIcon(config.last_sync_status)}
                        </div>
                        
                        <div className="text-sm text-muted-foreground space-y-1">
                          <div className="flex items-center gap-2">
                            <Database className="h-3 w-3" />
                            {config.spreadsheet_id} / {config.tab_name}
                          </div>
                          <div className="flex items-center gap-2">
                            <Clock className="h-3 w-3" />
                            Last sync: {formatDate(config.last_sync_at)}
                          </div>
                          {config.last_sync_details && (
                            <div className="text-xs">
                              Created: {config.last_sync_details.records_created}, 
                              Updated: {config.last_sync_details.records_updated}, 
                              Duration: {formatDuration(config.last_sync_details.duration_ms)}
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <Button
                          onClick={() => handleSync(config)}
                          disabled={syncing === config.id || !config.is_active}
                          size="sm"
                          variant="outline"
                          className="flex items-center gap-2"
                        >
                          {syncing === config.id ? (
                            <RefreshCw className="h-3 w-3 animate-spin" />
                          ) : (
                            <Play className="h-3 w-3" />
                          )}
                          {syncing === config.id ? 'Syncing...' : 'Sync'}
                        </Button>
                        
                        <Button
                          onClick={() => handleEdit(config)}
                          size="sm"
                          variant="outline"
                        >
                          <Edit className="h-3 w-3" />
                        </Button>
                        
                        <Button
                          onClick={() => {
                            setPendingDeleteId(config.id);
                            setDeleteDialogOpen(true);
                          }}
                          size="sm"
                          variant="outline"
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </Card>

          {/* Recent Sync Logs */}
          {showLogs && (
            <Card className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold">Recent Sync Logs</h2>
                <Button
                  onClick={loadLogs}
                  variant="outline"
                  size="sm"
                  disabled={logsLoading}
                  className="flex items-center gap-2"
                >
                  <RefreshCw className={`h-4 w-4 ${logsLoading ? 'animate-spin' : ''}`} />
                  Refresh
                </Button>
              </div>

              {logsLoading ? (
                <div className="flex justify-center py-8">
                  <Loader />
                </div>
              ) : logs.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  No sync logs found
                </div>
              ) : (
                <div className="space-y-3">
                  {logs.map((log) => (
                    <div
                      key={log.id}
                      className="p-3 border rounded-lg text-sm"
                    >
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          {getStatusIcon(log.status)}
                          <span className="font-medium">
                            {log.spreadsheet_sync_configs?.name || 'Unknown Config'}
                          </span>
                          <Badge variant={log.status === 'completed' ? 'default' : log.status === 'failed' ? 'destructive' : 'secondary'}>
                            {log.status}
                          </Badge>
                        </div>
                        <span className="text-muted-foreground">
                          {formatDate(log.started_at)}
                        </span>
                      </div>
                      
                      {log.status === 'completed' && (
                        <div className="text-muted-foreground">
                          Created: {log.records_created}, Updated: {log.records_updated}, 
                          Skipped: {log.records_skipped}, Duration: {formatDuration(log.duration_ms)}
                        </div>
                      )}
                      
                      {log.status === 'failed' && log.errors && log.errors.length > 0 && (
                        <div className="text-red-600 text-xs mt-1">
                          Error: {log.errors[0]}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </Card>
          )}

          {/* Delete Confirmation Dialog */}
          <ConfirmDialog
            open={deleteDialogOpen}
            title="Delete Sync Configuration?"
            description="This action cannot be undone and will permanently remove the sync configuration and all its logs."
            checkboxLabel="I am sure I want to delete this configuration"
            confirmLabel="Delete"
            onConfirm={handleDelete}
            onCancel={() => {
              setDeleteDialogOpen(false);
              setPendingDeleteId(null);
            }}
          />
        </div>
      </div>
    </AdminGuard>
  );
}