"use client";

import { useState, Suspense } from "react";
import { trpc } from "@/utils/trpc";
import { AuthDebugPanel } from "@/components/debug/auth-debug";
import { SearchBar } from "@/components/search-bar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Loader from "@/components/loader";
import { toast } from "sonner";
import { Plus, BarChart3, X, Database, Settings, RefreshCw } from "lucide-react";
import { ConfirmDialog } from "@/components/ui/confirm-dialog";


// Import new components and hooks
import { AdminGuard } from "@/components/admin/guards/AdminGuard";
import { ContentCard } from "@/components/admin/content/ContentCard";
import { Pagination } from "@/components/admin/Pagination";
import { BasicInfoSection } from "@/components/admin/forms/sections/BasicInfoSection";
import { MetricInput } from "@/components/admin/forms/fields/MetricInput";
import { MetadataManagement } from "@/components/admin/MetadataManagement";
import { MultiSelectDropdown } from "@/components/admin/forms/fields/MultiSelectDropdown";
import { TestimonialForm } from "@/components/admin/forms/TestimonialForm";
import { MediaMetricsManagement } from "@/components/admin/MediaMetricsManagement";
import { useAdminAuth } from "@/hooks/useAdminAuth";
import { useContentManagement } from "@/hooks/useContentManagement";
import { useSearchAndFilter } from "@/hooks/useSearchAndFilter";
import { useMetadataManagement } from "@/hooks/useMetadataManagement";

// Import types and constants
import type { ContentFormData } from "@/types/admin";
import { CONTENT_TYPES, CONTENT_CATEGORIES } from "@/lib/constants/content";
import { extractHostFromUrl } from "@/lib/utils/admin";

function AdminPageContent() {
  const [activeTab, setActiveTab] = useState<'content' | 'metadata' | 'testimonials' | 'sync-configs' | 'media-metrics'>('content');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);
  const [showForm, setShowForm] = useState(false);

  const { userProfile } = useAdminAuth();
  const {
    searchQuery,
    filters,
    isSearchActive,
    handleSearch,
    handleFilter,
    handleSort
  } = useSearchAndFilter();

  const {
    formData,
    setFormData,
    formErrors,
    isEditing,
    contentQuery,
    createContentMutation,
    updateContentMutation,
    deleteContentMutation,
    handleSubmit,
    handleEdit,
    handleCancelEdit,
    handleDelete
  } = useContentManagement(currentPage, pageSize);

  const {
    metadataQuery,
    handleAddContentType,
    handleAddContentCategory,
  } = useMetadataManagement(
    activeTab === 'sync-configs' || activeTab === 'media-metrics' ? 'content' : activeTab, 
    showForm
  );

  // Get filtered content for search functionality
  const filteredContentQuery = trpc.content.public.getFilteredContent.useQuery({
    search: searchQuery,
    contentTypes: filters.contentTypes,
    categories: filters.categories,
    sortBy: filters.sortBy as "impressions" | "date" | "likes" | "retweets",
    sortOrder: filters.sortOrder as "desc" | "asc",
    limit: 50
  });

  const updateStatsMutation = trpc.stats.updateStats.useMutation({
    onSuccess: (data) => {
      toast.success(`Stats updated successfully! ${data.stats.totalImpressions} impressions, ${data.stats.totalContent} content pieces`);
    },
    onError: (error) => {
      toast.error(`Failed to update stats: ${error.message}`);
    },
  });

  const handleInputChange = (field: keyof ContentFormData, value: any) => {
    setFormData(prev => {
      const newData = {
        ...prev,
        [field]: value
      };

      // Auto-fill host when content_link is changed
      if (field === 'content_link' && typeof value === 'string' && value.trim()) {
        const extractedHost = extractHostFromUrl(value.trim());
        if (extractedHost && (!prev.host || prev.host === '')) {
          newData.host = extractedHost;
        }
      }

      return newData;
    });
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleEditWithForm = (content: any) => {
    handleEdit(content);
    setShowForm(true); // Ensure form is shown when editing
  };

  const handleCancelEditWithForm = () => {
    handleCancelEdit();
    setShowForm(false); // Hide form when canceling edit
  };

  if (process.env.NODE_ENV === 'development') {
    console.log('Admin page loaded');
    console.log('🔍 [ADMIN] Search state:', {
      searchQuery,
      filters,
      isSearchActive,
      filteredResults: filteredContentQuery.data?.length,
      allContent: contentQuery.data?.data?.length,
    });
  }

  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [pendingDeleteId, setPendingDeleteId] = useState<number | null>(null);

  const requestDelete = (id: number) => {
    console.log("[ADMIN] Request delete clicked", { id });
    setPendingDeleteId(id);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = () => {
    if (pendingDeleteId == null) return;
    console.log("[ADMIN] Confirming deletion", { pendingDeleteId });
    deleteContentMutation.mutate({ id: pendingDeleteId });
    setDeleteDialogOpen(false);
    setPendingDeleteId(null);
  };

  const cancelDelete = () => {
    console.log("[ADMIN] Delete cancelled", { pendingDeleteId });
    setDeleteDialogOpen(false);
    setPendingDeleteId(null);
  };

  return (
    <div className="min-h-screen bg-background p-8">
      <AuthDebugPanel />
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold">Admin Dashboard</h1>
            <p className="text-muted-foreground">
              Welcome back, {userProfile?.full_name || userProfile?.email}
            </p>
          </div>
          <div className="flex gap-2">
            <Button
              onClick={() => updateStatsMutation.mutate()}
              variant="outline"
              className="flex items-center gap-2"
              disabled={updateStatsMutation.isPending}
            >
              <BarChart3 className="h-4 w-4" />
              {updateStatsMutation.isPending ? "Updating..." : "Update Stats"}
            </Button>
            {activeTab === 'content' && (
              isEditing ? (
                <Button
                  onClick={handleCancelEditWithForm}
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  <X className="h-4 w-4" />
                  Cancel Edit
                </Button>
              ) : (
                <Button
                  onClick={() => setShowForm(!showForm)}
                  className="flex items-center gap-2"
                >
                  <Plus className="h-4 w-4" />
                  Add Content
                </Button>
              )
            )}
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="mb-8">
          <div className="border-b border-border">
            <nav className="-mb-px flex space-x-8" aria-label="Tabs">
              <button
                onClick={() => setActiveTab('content')}
                className={`whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'content'
                    ? 'border-primary text-primary'
                    : 'border-transparent text-muted-foreground hover:text-foreground hover:border-muted-foreground'
                }`}
              >
                <div className="flex items-center gap-2">
                  <Database className="h-4 w-4" />
                  Content Management
                </div>
              </button>
              <button
                onClick={() => setActiveTab('metadata')}
                className={`whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'metadata'
                    ? 'border-primary text-primary'
                    : 'border-transparent text-muted-foreground hover:text-foreground hover:border-muted-foreground'
                }`}
              >
                <div className="flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  Metadata Management
                </div>
              </button>
              <button
                onClick={() => setActiveTab('testimonials')}
                className={`whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'testimonials'
                    ? 'border-primary text-primary'
                    : 'border-transparent text-muted-foreground hover:text-foreground hover:border-muted-foreground'
                }`}
              >
                <div className="flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  Testimonials
                </div>
              </button>
              <button
                onClick={() => setActiveTab('sync-configs')}
                className={`whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'sync-configs'
                    ? 'border-primary text-primary'
                    : 'border-transparent text-muted-foreground hover:text-foreground hover:border-muted-foreground'
                }`}
              >
                <div className="flex items-center gap-2">
                  <RefreshCw className="h-4 w-4" />
                  Sync Configurations
                </div>
              </button>
              <button
                onClick={() => setActiveTab('media-metrics')}
                className={`whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'media-metrics'
                    ? 'border-primary text-primary'
                    : 'border-transparent text-muted-foreground hover:text-foreground hover:border-muted-foreground'
                }`}
              >
                <div className="flex items-center gap-2">
                  <BarChart3 className="h-4 w-4" />
                  Media Metrics
                </div>
              </button>
            </nav>
          </div>
        </div>

        {/* Content Tab */}
        {activeTab === 'content' && (
          <>
            {/* Search Bar */}
            <div className="mb-8">
              <SearchBar
                onSearch={handleSearch}
                onFilter={handleFilter}
                onSort={handleSort}
                placeholder="Search admin content..."
                filters={filters}
                searchQuery={searchQuery}
              />
            </div>

            {/* Create Content Form */}
            {(showForm || isEditing) && (
              <Card className="p-6 mb-8">
                <h2 className="text-xl font-semibold mb-4">
                  {isEditing ? "Edit Content" : "Create New Content"}
                </h2>
                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Basic Information Section */}
                  <BasicInfoSection
                    formData={formData}
                    formErrors={formErrors}
                    onChange={handleInputChange}
                  />

                  {/* Content Metrics Section */}
                  <div className="space-y-4">
                    <h3 className="text-sm font-medium text-muted-foreground uppercase tracking-wide border-b pb-2">Content Metrics</h3>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                      <MetricInput
                        id="content_views"
                        label="Views"
                        value={formData.content_views}
                        onChange={(value) => handleInputChange('content_views', value)}
                        placeholder="Video views (e.g., 1.2m)"
                      />
                      <MetricInput
                        id="content_listeners"
                        label="Listeners"
                        value={formData.content_listeners}
                        onChange={(value) => handleInputChange('content_listeners', value)}
                        placeholder="Audio listeners (e.g., 500k)"
                      />
                      <MetricInput
                        id="twitter_impressions"
                        label="Impressions"
                        value={formData.twitter_impressions}
                        onChange={(value) => handleInputChange('twitter_impressions', value)}
                        placeholder="Twitter impressions (e.g., 2.5m)"
                      />
                      <MetricInput
                        id="content_follow_increase"
                        label="Follow Increase"
                        value={formData.content_follow_increase}
                        onChange={(value) => handleInputChange('content_follow_increase', value)}
                        placeholder="New followers (e.g., 1.2k)"
                      />
                    </div>
                  </div>

                  {/* Content Details Section */}
                  <div className="space-y-4">
                    <h3 className="text-sm font-medium text-muted-foreground uppercase tracking-wide border-b pb-2">Content Details</h3>
                    <div>
                      <Label htmlFor="content_description">Description (optional)</Label>
                      <Input
                        id="content_description"
                        value={formData.content_description || ''}
                        onChange={(e) => handleInputChange('content_description', e.target.value || null)}
                        placeholder="Brief description of the content..."
                      />
                    </div>

                    <div>
                      <Label htmlFor="content_tags">Tags (comma-separated)</Label>
                      <Input
                        id="content_tags"
                        value={formData.content_tags.join(', ')}
                        onChange={(e) => {
                          const array = e.target.value.split(',').map(item => item.trim()).filter(Boolean);
                          handleInputChange('content_tags', array);
                        }}
                        placeholder="tag1, tag2, tag3"
                      />
                    </div>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div>
                        <MultiSelectDropdown
                          label="Content Types"
                          value={formData.content_types}
                          options={metadataQuery.data?.content_types?.map(type => type.value) || CONTENT_TYPES}
                          onChange={(value) => handleInputChange('content_types', value)}
                          placeholder="Select content types..."
                          canAddNew={true}
                          onAddNew={handleAddContentType}
                          addNewLabel="Content Type"
                        />
                      </div>

                      <div>
                        <MultiSelectDropdown
                          label="Categories"
                          value={formData.content_categories}
                          options={metadataQuery.data?.content_categories?.map(cat => cat.value) || CONTENT_CATEGORIES}
                          onChange={(value) => handleInputChange('content_categories', value)}
                          placeholder="Select categories..."
                          canAddNew={true}
                          onAddNew={handleAddContentCategory}
                          addNewLabel="Category"
                        />
                      </div>
                    </div>
                  </div>

                  <div className="flex gap-2 pt-4">
                    <Button
                      type="submit"
                      disabled={createContentMutation.isPending || updateContentMutation.isPending}
                    >
                      {isEditing
                        ? (updateContentMutation.isPending ? "Updating..." : "Update Content")
                        : (createContentMutation.isPending ? "Creating..." : "Create Content")
                      }
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={isEditing ? handleCancelEditWithForm : () => setShowForm(false)}
                    >
                      Cancel
                    </Button>
                  </div>
                </form>
              </Card>
            )}

            {/* Content List */}
            <Card className="p-6">
              <h2 className="text-xl font-semibold mb-4">
                {isSearchActive ? `Search Results${searchQuery ? ` for "${searchQuery}"` : ''}` : 'Manage Content'}
              </h2>

              {(isSearchActive ? filteredContentQuery.isLoading : contentQuery.isLoading) ? (
                <div className="flex justify-center py-8">
                  <Loader />
                </div>
              ) : (
                <div className="space-y-4">
                  {(isSearchActive ? filteredContentQuery.data : contentQuery.data?.data)?.map((content) => (
                    <ContentCard
                      key={content.id}
                      content={content}
                      onEdit={handleEditWithForm}
                      onDelete={requestDelete}
                      isDeleting={deleteContentMutation.isPending}
                    />
                  ))}

                  {(isSearchActive ? filteredContentQuery.data : contentQuery.data?.data)?.length === 0 && (
                    <div className="text-center py-8 text-muted-foreground">
                      {isSearchActive
                        ? "No content found matching your search criteria."
                        : "No content found. Create your first entry!"
                      }
                    </div>
                  )}
                </div>
              )}

              {/* Pagination for non-search results */}
              {!isSearchActive && contentQuery.data && contentQuery.data.totalPages > 1 && (
                <Pagination
                  currentPage={currentPage}
                  totalPages={contentQuery.data.totalPages}
                  onPageChange={handlePageChange}
                  totalItems={contentQuery.data.total}
                  itemsPerPage={pageSize}
                />
              )}
            </Card>
          </>
        )}

        {/* Metadata Tab */}
        {activeTab === 'metadata' && (
          <MetadataManagement activeTab={activeTab} showForm={showForm} />
        )}
        <ConfirmDialog
          open={deleteDialogOpen}
          title="Delete this content?"
          description="This action cannot be undone and will permanently remove the content item."
          checkboxLabel="I am sure I want to delete this content"
          confirmLabel={deleteContentMutation.isPending ? "Deleting..." : "Delete"}
          loading={deleteContentMutation.isPending}
          onConfirm={confirmDelete}
          onCancel={cancelDelete}
        />


        {/* Testimonials Tab */}
        {activeTab === 'testimonials' && (
          <div className="space-y-8">
            <div className="w-full flex flex-col items-center justify-center">
              <div className="text-center mb-8">
                <h2 className="text-2xl font-bold mb-2">Testimonial Management</h2>
                <p className="text-muted-foreground">
                  Create and manage testimonials with screenshots and project details. Testimonials showcase positive feedback, case studies, and success stories.
                </p>
              </div>

              <div className="w-full max-w-4xl">
                <TestimonialForm
                  onSuccess={() => {
                    toast.success("Testimonial created successfully!");
                    // Optionally switch back to content tab to see the new testimonial
                    setActiveTab('content');
                  }}
                />
              </div>
            </div>
          </div>
        )}

        {/* Sync Configs Tab */}
        {activeTab === 'sync-configs' && (
          <div className="space-y-8">
            <div className="w-full">
              <div className="text-center mb-8">
                <h2 className="text-2xl font-bold mb-2">Spreadsheet Sync Configurations</h2>
                <p className="text-muted-foreground">
                  This feature has been moved to a dedicated page for better management. 
                  Click the button below to access the full sync configuration interface.
                </p>
              </div>

              <div className="flex justify-center">
                <Button
                  onClick={() => window.open('/admin/sync-configs', '_blank')}
                  className="flex items-center gap-2"
                  size="lg"
                >
                  <RefreshCw className="h-5 w-5" />
                  Open Sync Configuration Manager
                </Button>
              </div>
              
              <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-6 border rounded-lg">
                  <Database className="h-8 w-8 mx-auto mb-3 text-muted-foreground" />
                  <h3 className="font-semibold mb-2">Multiple Spreadsheets</h3>
                  <p className="text-sm text-muted-foreground">
                    Configure sync for multiple Google Sheets tabs simultaneously
                  </p>
                </div>
                
                <div className="text-center p-6 border rounded-lg">
                  <RefreshCw className="h-8 w-8 mx-auto mb-3 text-muted-foreground" />
                  <h3 className="font-semibold mb-2">Automated Sync</h3>
                  <p className="text-sm text-muted-foreground">
                    Runs automatically every 30 minutes to keep data up-to-date
                  </p>
                </div>
                
                <div className="text-center p-6 border rounded-lg">
                  <BarChart3 className="h-8 w-8 mx-auto mb-3 text-muted-foreground" />
                  <h3 className="font-semibold mb-2">Monitoring & Logs</h3>
                  <p className="text-sm text-muted-foreground">
                    Track sync status, view logs, and monitor performance
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Media Metrics Tab */}
        {activeTab === 'media-metrics' && (
          <MediaMetricsManagement />
        )}

      </div>
    </div>
  );
}

export default function AdminPage() {
  return (
    <AdminGuard>
      <Suspense fallback={<div className="min-h-screen flex items-center justify-center"><Loader /></div>}>
        <AdminPageContent />
      </Suspense>
    </AdminGuard>
  );
}