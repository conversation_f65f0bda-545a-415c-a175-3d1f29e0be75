import { readCsvFile, parseCsvData } from './services/csv-sync';

async function testDetailedSync() {
  try {
    console.log('🔍 Detailed sync test with Joey.csv...');
    
    // Read and parse the CSV
    const csvPath = '/Users/<USER>/Downloads/Coding/IBC/ibc-final-dashboard/docs/Joey.csv';
    const csvContent = await readCsvFile(csvPath);
    const contentPieces = parseCsvData(csvContent);
    
    console.log(`\n📊 Processing ${contentPieces.length} content pieces from Joey.csv\n`);
    
    // Show first 5 entries with full detail
    console.log('--- SAMPLE PROCESSED ENTRIES ---');
    for (let i = 0; i < Math.min(5, contentPieces.length); i++) {
      const piece = contentPieces[i];
      console.log(`\n🔹 Entry ${i + 1}:`);
      console.log(`   Link: ${piece.content_link}`);
      console.log(`   Host: ${piece.host}`);
      console.log(`   Client: ${piece.content_account?.join(', ')}`);
      console.log(`   Content Types: ${piece.content_types?.join(', ')}`);
      console.log(`   Twitter Type: ${piece.twitter_content_type || 'null'}`);
      console.log(`   Categories: ${piece.content_categories?.join(', ')}`);
      console.log(`   Title: ${piece.content_title}`);
      console.log(`   Views: ${piece.content_views?.toLocaleString()}`);
      console.log(`   Impressions: ${piece.twitter_impressions?.toLocaleString()}`);
      console.log(`   Follow Increase: ${piece.content_follow_increase?.toLocaleString()}`);
      console.log(`   Date: ${piece.content_created_date}`);
      console.log(`   Screenshots: ${piece.screenshot_urls?.length || 0} URLs`);
    }
    
    // Show content type distribution
    console.log('\n--- CONTENT TYPE DISTRIBUTION ---');
    const typeCount: Record<string, number> = {};
    const twitterTypeCount: Record<string, number> = {};
    const categoryCount: Record<string, number> = {};
    
    contentPieces.forEach(piece => {
      piece.content_types?.forEach(type => {
        typeCount[type] = (typeCount[type] || 0) + 1;
      });
      
      if (piece.twitter_content_type) {
        twitterTypeCount[piece.twitter_content_type] = (twitterTypeCount[piece.twitter_content_type] || 0) + 1;
      }
      
      piece.content_categories?.forEach(cat => {
        categoryCount[cat] = (categoryCount[cat] || 0) + 1;
      });
    });
    
    console.log('Content Types:');
    Object.entries(typeCount).forEach(([type, count]) => {
      console.log(`  ${type}: ${count}`);
    });
    
    console.log('\nTwitter Content Types:');
    Object.entries(twitterTypeCount).forEach(([type, count]) => {
      console.log(`  ${type}: ${count}`);
    });
    
    console.log('\nCategories:');
    Object.entries(categoryCount).forEach(([cat, count]) => {
      console.log(`  ${cat}: ${count}`);
    });
    
    // Show host extraction results
    console.log('\n--- HOST EXTRACTION SAMPLES ---');
    const hostSamples = contentPieces.slice(0, 10).map(piece => ({
      url: piece.content_link,
      host: piece.host,
      client: piece.content_account?.[0]
    }));
    
    hostSamples.forEach((sample, i) => {
      console.log(`${i + 1}. ${sample.url} → Host: "${sample.host}" (Client: ${sample.client})`);
    });
    
    // Show data validation
    console.log('\n--- DATA VALIDATION ---');
    const missingRequiredFields = contentPieces.filter(piece => 
      !piece.content_link || !piece.host || !piece.content_account?.length
    );
    
    console.log(`✅ Entries with all required fields: ${contentPieces.length - missingRequiredFields.length}`);
    if (missingRequiredFields.length > 0) {
      console.log(`⚠️  Entries missing required fields: ${missingRequiredFields.length}`);
    }
    
    const totalViews = contentPieces.reduce((sum, piece) => sum + (piece.content_views || 0), 0);
    const totalImpressions = contentPieces.reduce((sum, piece) => sum + (piece.twitter_impressions || 0), 0);
    const totalFollowIncrease = contentPieces.reduce((sum, piece) => sum + (piece.content_follow_increase || 0), 0);
    
    console.log(`📈 Total Views: ${totalViews.toLocaleString()}`);
    console.log(`📈 Total Impressions: ${totalImpressions.toLocaleString()}`);
    console.log(`📈 Total Follow Increase: ${totalFollowIncrease.toLocaleString()}`);
    
    console.log('\n✅ Detailed sync test completed successfully!');
    
  } catch (error) {
    console.error('❌ Detailed test failed:', error);
  }
}

// Run the test
testDetailedSync();