/**
 * Environment variable validation for security
 */

interface RequiredEnvVars {
  NEXT_PUBLIC_SUPABASE_URL: string;
  SUPABASE_ANON_KEY?: string;
  NEXT_PUBLIC_SUPABASE_ANON_KEY?: string;
  DATABASE_URL?: string;
  CORS_ORIGIN?: string;
}

/**
 * Validates that all required environment variables are present
 */
export function validateEnvironmentVariables(): RequiredEnvVars {
  const errors: string[] = [];

  // Required client-side variables
  const NEXT_PUBLIC_SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
  if (!NEXT_PUBLIC_SUPABASE_URL) {
    errors.push('NEXT_PUBLIC_SUPABASE_URL is required');
  }

  // Check for Supabase anonymous key (either server or client version)
  const SUPABASE_ANON_KEY = process.env.SUPABASE_ANON_KEY;
  const NEXT_PUBLIC_SUPABASE_ANON_KEY = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  
  if (!SUPABASE_ANON_KEY && !NEXT_PUBLIC_SUPABASE_ANON_KEY) {
    errors.push('Either SUPABASE_ANON_KEY or NEXT_PUBLIC_SUPABASE_ANON_KEY is required');
  }

  // Validate URL format for Supabase URL
  if (NEXT_PUBLIC_SUPABASE_URL) {
    try {
      const url = new URL(NEXT_PUBLIC_SUPABASE_URL);
      if (!url.protocol.match(/^https?:$/)) {
        errors.push('NEXT_PUBLIC_SUPABASE_URL must use http or https protocol');
      }
    } catch {
      errors.push('NEXT_PUBLIC_SUPABASE_URL must be a valid URL');
    }
  }

  // Server-side only validation (skip if this is running on client)
  if (typeof window === 'undefined') {
    // Validate DATABASE_URL if it exists
    const DATABASE_URL = process.env.DATABASE_URL;
    if (DATABASE_URL) {
      try {
        const url = new URL(DATABASE_URL);
        if (!url.protocol.startsWith('postgres')) {
          errors.push('DATABASE_URL must use postgresql protocol');
        }
      } catch {
        errors.push('DATABASE_URL must be a valid PostgreSQL connection string');
      }
    }

    // Validate CORS_ORIGIN format if it exists
    const CORS_ORIGIN = process.env.CORS_ORIGIN;
    if (CORS_ORIGIN && CORS_ORIGIN !== '*') {
      try {
        new URL(CORS_ORIGIN);
      } catch {
        errors.push('CORS_ORIGIN must be a valid URL or "*"');
      }
    }
  }

  if (errors.length > 0) {
    const errorMessage = `Environment validation failed:\n${errors.map(e => `  - ${e}`).join('\n')}`;
    throw new Error(errorMessage);
  }

  return {
    NEXT_PUBLIC_SUPABASE_URL: NEXT_PUBLIC_SUPABASE_URL!,
    SUPABASE_ANON_KEY,
    NEXT_PUBLIC_SUPABASE_ANON_KEY,
    DATABASE_URL: process.env.DATABASE_URL,
    CORS_ORIGIN: process.env.CORS_ORIGIN,
  };
}

/**
 * Get validated environment variables with defaults
 */
export function getValidatedEnv() {
  return validateEnvironmentVariables();
}

/**
 * Validate environment on application startup
 */
export function validateEnvOnStartup() {
  try {
    validateEnvironmentVariables();
    console.log('✅ Environment validation passed');
  } catch (error) {
    console.error('❌ Environment validation failed:', error instanceof Error ? error.message : error);
    process.exit(1);
  }
}