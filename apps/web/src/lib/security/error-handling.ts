/**
 * Secure error handling utilities to prevent information disclosure
 */

import { TRPCError } from '@trpc/server';
import { errorLog } from './logging';

export interface SecureErrorOptions {
  publicMessage?: string;
  code?: 'BAD_REQUEST' | 'UNAUTHORIZED' | 'FORBIDDEN' | 'NOT_FOUND' | 'INTERNAL_SERVER_ERROR' | 'TOO_MANY_REQUESTS';
  logContext?: Record<string, any>;
}

/**
 * Create a secure error that logs sensitive details server-side
 * but only exposes safe information to the client
 */
export function createSecureError(
  internalMessage: string,
  error?: Error | unknown,
  options: SecureErrorOptions = {}
): TRPCError {
  const {
    publicMessage = 'An error occurred',
    code = 'INTERNAL_SERVER_ERROR',
    logContext = {}
  } = options;

  // Log the full error details server-side
  errorLog(`[SECURE_ERROR] ${internalMessage}`, error, logContext);

  // Return sanitized error to client
  return new TRPCError({
    code,
    message: publicMessage,
  });
}

/**
 * Handle database errors securely
 */
export function handleDatabaseError(
  error: unknown,
  operation: string,
  context?: Record<string, any>
): TRPCError {
  const isPostgresError = error && typeof error === 'object' && 'code' in error;
  
  // Log specific database error details internally
  errorLog(`Database error during ${operation}`, error, context);

  // Return generic message to client
  if (isPostgresError) {
    const pgError = error as { code: string; message: string; detail?: string };
    
    // Handle specific PostgreSQL errors with appropriate public messages
    switch (pgError.code) {
      case '23505': // Unique constraint violation
        return new TRPCError({
          code: 'BAD_REQUEST',
          message: 'This item already exists',
        });
      case '23503': // Foreign key constraint violation
        return new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Referenced item does not exist',
        });
      case '23502': // Not null constraint violation
        return new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Required field is missing',
        });
      case '42P01': // Undefined table
        return new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Service temporarily unavailable',
        });
      default:
        return new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Database operation failed',
        });
    }
  }

  return new TRPCError({
    code: 'INTERNAL_SERVER_ERROR',
    message: 'An unexpected error occurred',
  });
}

/**
 * Handle authentication errors
 */
export function handleAuthError(
  error: unknown,
  context?: Record<string, any>
): TRPCError {
  errorLog('Authentication error', error, context);

  // Don't reveal whether user exists or not
  return new TRPCError({
    code: 'UNAUTHORIZED',
    message: 'Authentication failed',
  });
}

/**
 * Handle authorization errors
 */
export function handleAuthorizationError(
  error: unknown,
  requiredPermission?: string,
  context?: Record<string, any>
): TRPCError {
  errorLog('Authorization error', error, { 
    ...context, 
    requiredPermission 
  });

  return new TRPCError({
    code: 'FORBIDDEN',
    message: 'Access denied',
  });
}

/**
 * Handle validation errors
 */
export function handleValidationError(
  error: unknown,
  field?: string,
  context?: Record<string, any>
): TRPCError {
  errorLog('Validation error', error, { ...context, field });

  if (field) {
    return new TRPCError({
      code: 'BAD_REQUEST',
      message: `Invalid ${field}`,
    });
  }

  return new TRPCError({
    code: 'BAD_REQUEST',
    message: 'Invalid request data',
  });
}

/**
 * Handle not found errors
 */
export function handleNotFoundError(
  resourceType: string = 'resource',
  context?: Record<string, any>
): TRPCError {
  errorLog(`${resourceType} not found`, undefined, context);

  return new TRPCError({
    code: 'NOT_FOUND',
    message: `${resourceType} not found`,
  });
}

/**
 * Generic error wrapper for tRPC procedures
 */
export function withSecureErrorHandling<T>(
  operation: () => Promise<T>,
  operationName: string,
  context?: Record<string, any>
): Promise<T> {
  return operation().catch((error) => {
    if (error instanceof TRPCError) {
      // Already a properly formatted tRPC error, re-throw
      throw error;
    }

    // Handle unexpected errors
    throw createSecureError(
      `Unexpected error in ${operationName}`,
      error,
      {
        publicMessage: 'An unexpected error occurred',
        logContext: context,
      }
    );
  });
}

/**
 * Sanitize error for client response
 */
export function sanitizeErrorForClient(error: unknown): {
  message: string;
  code?: string;
} {
  if (error instanceof TRPCError) {
    return {
      message: error.message,
      code: error.code,
    };
  }

  // Don't expose internal error details
  return {
    message: 'An error occurred',
    code: 'INTERNAL_SERVER_ERROR',
  };
}

/**
 * Error boundary for React components
 */
export class ErrorBoundary extends Error {
  constructor(
    public originalError: Error,
    public context?: Record<string, any>
  ) {
    super('Error boundary caught an error');
    this.name = 'ErrorBoundary';
    
    // Log the error with context
    errorLog('React error boundary triggered', originalError, context);
  }
}

/**
 * Validate and sanitize error messages
 */
export function sanitizeErrorMessage(message: string): string {
  // Remove potentially sensitive information patterns
  return message
    .replace(/password.*$/gi, '[REDACTED]')
    .replace(/token.*$/gi, '[REDACTED]')
    .replace(/key.*$/gi, '[REDACTED]')
    .replace(/secret.*$/gi, '[REDACTED]')
    .replace(/\b\d{3}-\d{2}-\d{4}\b/g, '[SSN]') // SSN pattern
    .replace(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, '[EMAIL]') // Email pattern
    .slice(0, 200); // Limit length
}