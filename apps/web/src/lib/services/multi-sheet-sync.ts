import { createClient } from '@supabase/supabase-js';
import { sheetsSyncService, type SyncResult } from './sheets-sync';

interface SyncConfig {
  id: string;
  name: string;
  spreadsheet_id: string;
  tab_name: string;
  is_active: boolean;
  sync_order: number;
  last_sync_at: string | null;
  last_sync_status: string | null;
}

interface SyncLogEntry {
  config_id: string;
  started_at: string;
  completed_at?: string;
  status: 'running' | 'completed' | 'failed' | 'cancelled';
  records_created: number;
  records_updated: number;
  records_skipped: number;
  error_count: number;
  errors?: any[];
  details?: any;
  duration_ms?: number;
}

interface MultiSyncResult {
  totalConfigs: number;
  successfulSyncs: number;
  failedSyncs: number;
  skippedSyncs: number;
  results: Array<{
    configId: string;
    configName: string;
    status: 'success' | 'failed' | 'skipped';
    result?: SyncResult;
    error?: string;
    duration: number;
  }>;
  totalDuration: number;
}

export class MultiSheetSyncService {
  private supabase;
  
  constructor() {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    // Use service role key to bypass RLS for sync operations
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY || process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error('Missing Supabase configuration. Need SUPABASE_SERVICE_KEY for sync operations.');
    }
    
    this.supabase = createClient(supabaseUrl, supabaseServiceKey);
  }
  
  /**
   * Get all active sync configurations ordered by sync_order
   */
  private async getActiveSyncConfigs(): Promise<SyncConfig[]> {
    const { data, error } = await this.supabase
      .from('spreadsheet_sync_configs')
      .select('*')
      .eq('is_active', true)
      .order('sync_order', { ascending: true })
      .order('name', { ascending: true });
    
    if (error) {
      console.error('Failed to fetch sync configurations:', error);
      throw new Error(`Failed to fetch sync configurations: ${error.message}`);
    }
    
    return data || [];
  }
  
  /**
   * Create a sync log entry
   */
  private async createSyncLog(entry: Omit<SyncLogEntry, 'id'>): Promise<string> {
    const { data, error } = await this.supabase
      .from('sync_logs')
      .insert([entry])
      .select('id')
      .single();
    
    if (error) {
      console.error('Failed to create sync log:', error);
      throw new Error(`Failed to create sync log: ${error.message}`);
    }
    
    return data.id;
  }
  
  /**
   * Update a sync log entry
   */
  private async updateSyncLog(id: string, updates: Partial<SyncLogEntry>): Promise<void> {
    const { error } = await this.supabase
      .from('sync_logs')
      .update(updates)
      .eq('id', id);
    
    if (error) {
      console.error('Failed to update sync log:', error);
      throw new Error(`Failed to update sync log: ${error.message}`);
    }
  }
  
  /**
   * Sync a single configuration
   */
  private async syncSingleConfig(config: SyncConfig): Promise<{
    status: 'success' | 'failed';
    result?: SyncResult;
    error?: string;
    duration: number;
  }> {
    const startTime = Date.now();
    let logId: string | null = null;
    
    try {
      console.log(`🔄 Starting sync for config: ${config.name} (${config.spreadsheet_id}/${config.tab_name})`);
      
      // Create sync log entry
      logId = await this.createSyncLog({
        config_id: config.id,
        started_at: new Date().toISOString(),
        status: 'running',
        records_created: 0,
        records_updated: 0,
        records_skipped: 0,
        error_count: 0
      });
      
      // Perform the actual sync
      const result = await sheetsSyncService.sync({
        useGoogleSheets: true,
        googleSheetsId: config.spreadsheet_id,
        googleSheetsTab: config.tab_name,
        dryRun: false
      });
      
      const duration = Date.now() - startTime;
      const completedAt = new Date().toISOString();
      
      // Update sync log with success
      if (logId) {
        await this.updateSyncLog(logId, {
          completed_at: completedAt,
          status: 'completed',
          records_created: result.created,
          records_updated: result.updated,
          records_skipped: result.skipped,
          error_count: result.errors.length,
          errors: result.errors.length > 0 ? result.errors : undefined,
          details: {
            totalProcessed: result.totalProcessed
          },
          duration_ms: duration
        });
      }
      
      console.log(`✅ Sync completed for config: ${config.name} (${duration}ms)`);
      return {
        status: 'success',
        result,
        duration
      };
      
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      console.error(`❌ Sync failed for config: ${config.name}`, error);
      
      // Update sync log with failure
      if (logId) {
        try {
          await this.updateSyncLog(logId, {
            completed_at: new Date().toISOString(),
            status: 'failed',
            error_count: 1,
            errors: [errorMessage],
            duration_ms: duration
          });
        } catch (logError) {
          console.error('Failed to update sync log with error:', logError);
        }
      }
      
      return {
        status: 'failed',
        error: errorMessage,
        duration
      };
    }
  }
  
  /**
   * Sync all active configurations
   */
  public async syncAll(options: { 
    maxConcurrent?: number;
    continueOnError?: boolean;
  } = {}): Promise<MultiSyncResult> {
    const { maxConcurrent = 3, continueOnError = true } = options;
    const overallStartTime = Date.now();
    
    console.log('🚀 Starting multi-sheet sync process...');
    
    try {
      // Get all active sync configurations
      const configs = await this.getActiveSyncConfigs();
      
      if (configs.length === 0) {
        console.log('📋 No active sync configurations found');
        return {
          totalConfigs: 0,
          successfulSyncs: 0,
          failedSyncs: 0,
          skippedSyncs: 0,
          results: [],
          totalDuration: Date.now() - overallStartTime
        };
      }
      
      console.log(`📋 Found ${configs.length} active sync configurations`);
      
      const result: MultiSyncResult = {
        totalConfigs: configs.length,
        successfulSyncs: 0,
        failedSyncs: 0,
        skippedSyncs: 0,
        results: [],
        totalDuration: 0
      };
      
      // Process syncs in batches to respect concurrency limit
      for (let i = 0; i < configs.length; i += maxConcurrent) {
        const batch = configs.slice(i, i + maxConcurrent);
        console.log(`🔄 Processing batch ${Math.floor(i/maxConcurrent) + 1} (${batch.length} configs)`);
        
        // Run batch in parallel
        const batchPromises = batch.map(async (config) => {
          const syncResult = await this.syncSingleConfig(config);
          return {
            configId: config.id,
            configName: config.name,
            ...syncResult
          };
        });
        
        const batchResults = await Promise.allSettled(batchPromises);
        
        // Process batch results
        for (const promiseResult of batchResults) {
          if (promiseResult.status === 'fulfilled') {
            const syncResult = promiseResult.value;
            result.results.push(syncResult);
            
            if (syncResult.status === 'success') {
              result.successfulSyncs++;
            } else {
              result.failedSyncs++;
              
              // If we're not continuing on error, stop processing
              if (!continueOnError) {
                console.log('🛑 Stopping sync process due to error and continueOnError=false');
                break;
              }
            }
          } else {
            // Promise was rejected
            console.error('🔥 Batch promise rejected:', promiseResult.reason);
            result.failedSyncs++;
            result.results.push({
              configId: 'unknown',
              configName: 'unknown',
              status: 'failed',
              error: promiseResult.reason instanceof Error ? promiseResult.reason.message : 'Promise rejected',
              duration: 0
            });
          }
        }
        
        // Stop processing if we hit an error and shouldn't continue
        if (!continueOnError && result.failedSyncs > 0) {
          break;
        }
      }
      
      result.totalDuration = Date.now() - overallStartTime;
      
      console.log(`🏁 Multi-sheet sync completed in ${result.totalDuration}ms:`);
      console.log(`   ✅ Successful: ${result.successfulSyncs}`);
      console.log(`   ❌ Failed: ${result.failedSyncs}`);
      console.log(`   ⏭️ Skipped: ${result.skippedSyncs}`);
      
      return result;
      
    } catch (error) {
      console.error('💥 Multi-sheet sync process failed:', error);
      throw error;
    }
  }
  
  /**
   * Get sync statistics for monitoring
   */
  public async getSyncStats(days: number = 7): Promise<{
    totalSyncs: number;
    successfulSyncs: number;
    failedSyncs: number;
    averageDuration: number;
    recentErrors: string[];
  }> {
    const since = new Date(Date.now() - (days * 24 * 60 * 60 * 1000)).toISOString();
    
    const { data, error } = await this.supabase
      .from('sync_logs')
      .select('status, duration_ms, errors')
      .gte('started_at', since)
      .neq('status', 'running'); // Exclude currently running syncs
    
    if (error) {
      console.error('Failed to fetch sync stats:', error);
      throw new Error(`Failed to fetch sync stats: ${error.message}`);
    }
    
    const logs = data || [];
    const successful = logs.filter(log => log.status === 'completed');
    const failed = logs.filter(log => log.status === 'failed');
    
    // Calculate average duration (only for completed syncs)
    const completedWithDuration = successful.filter(log => log.duration_ms != null);
    const averageDuration = completedWithDuration.length > 0 
      ? Math.round(completedWithDuration.reduce((sum, log) => sum + log.duration_ms, 0) / completedWithDuration.length)
      : 0;
    
    // Get recent errors
    const recentErrors = failed
      .filter(log => log.errors && log.errors.length > 0)
      .slice(0, 10) // Get last 10 errors
      .flatMap(log => log.errors)
      .filter((error, index, arr) => arr.indexOf(error) === index); // Deduplicate
    
    return {
      totalSyncs: logs.length,
      successfulSyncs: successful.length,
      failedSyncs: failed.length,
      averageDuration,
      recentErrors
    };
  }
}

// Export singleton instance
export const multiSheetSyncService = new MultiSheetSyncService();