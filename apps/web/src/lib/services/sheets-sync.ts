import { google } from 'googleapis';
import { createClient } from '@supabase/supabase-js';
import { parseCsvData, readCsvFile, type CsvRow } from './csv-sync';
import type { ContentPiece } from '../server/supabase';

export interface SyncResult {
  created: number;
  updated: number;
  skipped: number;
  errors: string[];
  totalProcessed: number;
}

interface SyncOptions {
  useGoogleSheets?: boolean;
  csvFilePath?: string;
  googleSheetsId?: string;
  googleSheetsTab?: string;
  dryRun?: boolean;
}

interface ContentTypeMapping {
  source_value: string;
  target_content_type: string;
  twitter_content_type: string | null;
  description?: string;
}

export class SheetsSyncService {
  private supabase;
  private contentTypeMappings: Map<string, ContentTypeMapping> = new Map();
  
  constructor() {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    // For sync operations, we need service role key to bypass RLS
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY || process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error('Missing Supabase configuration. Need SUPABASE_SERVICE_KEY for sync operations.');
    }
    
    this.supabase = createClient(supabaseUrl, supabaseServiceKey);
  }
  
  private async loadContentTypeMappings(): Promise<void> {
    try {
      const { data, error } = await this.supabase
        .from('content_type_mappings')
        .select('source_value, target_content_type, twitter_content_type, description')
        .eq('is_active', true);
      
      if (error) {
        console.warn('Failed to load content type mappings, using fallbacks:', error);
        return;
      }
      
      this.contentTypeMappings.clear();
      data?.forEach(mapping => {
        this.contentTypeMappings.set(mapping.source_value, mapping);
      });
      
      console.log(`Loaded ${this.contentTypeMappings.size} content type mappings from database`);
    } catch (error) {
      console.warn('Error loading content type mappings, using fallbacks:', error);
    }
  }
  
  private async getGoogleSheetsAuth() {
    const serviceAccountEmail = process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL;
    const privateKey = process.env.GOOGLE_PRIVATE_KEY?.replace(/\\n/g, '\n');
    
    if (!serviceAccountEmail || !privateKey) {
      throw new Error('Missing Google Sheets credentials');
    }
    
    const auth = new google.auth.JWT({
      email: serviceAccountEmail,
      key: privateKey,
      scopes: ['https://www.googleapis.com/auth/spreadsheets.readonly']
    });
    
    await auth.authorize();
    return auth;
  }
  
  private async fetchGoogleSheetsData(sheetsId: string, tabName: string = 'Sheet1'): Promise<CsvRow[]> {
    const auth = await this.getGoogleSheetsAuth();
    const sheets = google.sheets({ version: 'v4', auth });
    
    const response = await sheets.spreadsheets.values.get({
      spreadsheetId: sheetsId,
      range: `${tabName}!A:J`, // Columns A to J (all our data columns)
    });
    
    const rows = response.data.values;
    if (!rows || rows.length === 0) {
      throw new Error('No data found in sheet');
    }
    
    // First row is headers
    const headers = rows[0];
    
    // Convert rows to objects
    return rows.slice(1).map(row => {
      const obj: any = {};
      headers.forEach((header, index) => {
        obj[header] = row[index] || '';
      });
      return obj as CsvRow;
    });
  }
  
  private async fetchCsvData(filePath: string): Promise<Partial<ContentPiece>[]> {
    const csvContent = await readCsvFile(filePath);
    return parseCsvData(csvContent);
  }
  
  private transformCsvRowWithMappings(row: CsvRow): Partial<ContentPiece> | null {
    try {
      // Skip rows without a show link
      if (!row['Show Link']) {
        console.warn('Skipping row without Show Link');
        return null;
      }
      
      const host = this.extractHostFromUrl(row['Show Link'], row['Client Name']);
      
      // Use database mappings or fallback to hardcoded values
      const showType = row['Show Type']?.trim();
      const dbMapping = this.contentTypeMappings.get(showType);
      
      let contentTypes: string[];
      let twitterContentType: string | null;
      
      if (dbMapping) {
        contentTypes = [dbMapping.target_content_type];
        twitterContentType = dbMapping.twitter_content_type;
      } else {
        // Import and use fallback functions
        const { mapShowTypeToContentType, getTwitterContentType } = require('./csv-sync');
        contentTypes = mapShowTypeToContentType(showType);
        twitterContentType = getTwitterContentType(showType);
      }
      
      return {
        content_link: row['Show Link'],
        host,
        content_account: row['Client Name'] ? [row['Client Name']] : [],
        content_created_date: this.parseDate(row['Date']),
        content_types: contentTypes,
        twitter_content_type: twitterContentType,
        content_categories: this.mapTopicToCategories(row['Show Topic']),
        content_title: row['Show Title'] || null,
        content_description: row['Show Title'] || null,
        screenshot_urls: row['Report Link'] ? [row['Report Link']] : [],
        content_views: this.parseNumber(row['Views / Listeners']),
        twitter_impressions: this.parseNumber(row['Impressions']),
        content_follow_increase: this.parseNumber(row['Follow Increase']),
        // Default values
        content_tags: [],
        content_listeners: this.parseNumber(row['Views / Listeners']),
        twitter_likes: 0,
        twitter_retweets: 0
      };
    } catch (error) {
      console.error('Failed to transform CSV row:', error, row);
      return null;
    }
  }
  
  private extractHostFromUrl(url: string, clientName: string): string {
    try {
      const match = url.match(/(?:twitter\.com|x\.com)\/(@?[\w]+)(?:\/|$)/);
      if (match && match[1] && match[1] !== 'i') {
        return match[1].replace('@', '').toLowerCase();
      }
    } catch (error) {
      console.warn(`Failed to extract host from URL: ${url}`, error);
    }
    
    return clientName.toLowerCase().replace(/\s+/g, '').replace(/[^a-z0-9]/g, '');
  }
  
  private parseNumber(value: string): number {
    if (!value || value === '-' || value === '') return 0;
    
    value = value.replace('~', '').trim();
    
    if (value.includes('M')) {
      const num = parseFloat(value.replace('M', '').trim());
      return Math.round(num * 1000000);
    }
    
    if (value.toLowerCase().includes('k')) {
      const num = parseFloat(value.replace(/k/i, '').trim());
      return Math.round(num * 1000);
    }
    
    value = value.replace(/,/g, '');
    const parsed = parseFloat(value);
    return isNaN(parsed) ? 0 : Math.round(parsed);
  }
  
  private parseDate(dateStr: string): string {
    try {
      const [day, month, year] = dateStr.split('/');
      const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
      return date.toISOString();
    } catch (error) {
      console.warn(`Failed to parse date: ${dateStr}`, error);
      return new Date().toISOString();
    }
  }
  
  private mapTopicToCategories(topic: string): string[] {
    const normalized = topic.toLowerCase().trim();
    
    const mapping: Record<string, string> = {
      'ai': 'ai',
      'defi': 'defi',
      'gaming': 'gaming',
      'memecoin': 'memecoin',
      'web3': 'web3',
      'web2': 'web2',
      'rwa': 'blockchain',
      'socialfi': 'web3',
      'multi-chain': 'blockchain',
      'countdown': 'news',
      'cth': 'news'
    };
    
    const category = mapping[normalized];
    return category ? [category] : [];
  }
  
  private async upsertContentPiece(contentPiece: Partial<ContentPiece>): Promise<'created' | 'updated' | 'skipped' | 'error'> {
    try {
      // Check if content already exists
      const { data: existing, error: fetchError } = await this.supabase
        .from('content_pieces')
        .select('id, updated_at')
        .eq('content_link', contentPiece.content_link!)
        .maybeSingle();
      
      if (fetchError) {
        console.error('Error checking existing content:', fetchError);
        return 'error';
      }
      
      if (existing) {
        // Update existing record
        const { error: updateError } = await this.supabase
          .from('content_pieces')
          .update({
            ...contentPiece,
            updated_at: new Date().toISOString()
          })
          .eq('id', existing.id);
        
        if (updateError) {
          console.error('Error updating content:', updateError);
          return 'error';
        }
        
        return 'updated';
      } else {
        // Create new record
        const { error: insertError } = await this.supabase
          .from('content_pieces')
          .insert([contentPiece]);
        
        if (insertError) {
          console.error('Error inserting content:', insertError);
          return 'error';
        }
        
        return 'created';
      }
    } catch (error) {
      console.error('Unexpected error in upsert:', error);
      return 'error';
    }
  }
  
  public async sync(options: SyncOptions): Promise<SyncResult> {
    const result: SyncResult = {
      created: 0,
      updated: 0,
      skipped: 0,
      errors: [],
      totalProcessed: 0
    };
    
    try {
      // Load content type mappings from database first
      await this.loadContentTypeMappings();
      
      let contentPieces: Partial<ContentPiece>[] = [];
      
      if (options.useGoogleSheets) {
        const sheetsId = options.googleSheetsId || process.env.GOOGLE_SHEETS_ID;
        const tabName = options.googleSheetsTab || process.env.GOOGLE_SHEETS_TAB || 'Sheet1';
        
        if (!sheetsId) {
          throw new Error('Google Sheets ID not provided');
        }
        
        console.log(`Fetching data from Google Sheets: ${sheetsId}, Tab: ${tabName}`);
        const rows = await this.fetchGoogleSheetsData(sheetsId, tabName);
        contentPieces = rows.map(row => this.transformCsvRowWithMappings(row)).filter(Boolean) as Partial<ContentPiece>[];
      } else {
        const csvPath = options.csvFilePath || process.env.CSV_FILE_PATH;
        
        if (!csvPath) {
          throw new Error('CSV file path not provided');
        }
        
        console.log(`Reading CSV file: ${csvPath}`);
        const csvContent = await readCsvFile(csvPath);
        const { parse } = await import('csv-parse/sync');
        const rows = parse(csvContent, {
          columns: true,
          skip_empty_lines: true,
          trim: true
        }) as CsvRow[];
        
        contentPieces = rows.map(row => this.transformCsvRowWithMappings(row)).filter(Boolean) as Partial<ContentPiece>[];
      }
      
      console.log(`Processing ${contentPieces.length} content pieces`);
      
      if (options.dryRun) {
        console.log('DRY RUN - No data will be saved');
        result.totalProcessed = contentPieces.length;
        return result;
      }
      
      // Process in batches to avoid overwhelming the database
      const batchSize = 10;
      for (let i = 0; i < contentPieces.length; i += batchSize) {
        const batch = contentPieces.slice(i, i + batchSize);
        
        await Promise.all(
          batch.map(async (piece) => {
            const status = await this.upsertContentPiece(piece);
            
            switch (status) {
              case 'created':
                result.created++;
                break;
              case 'updated':
                result.updated++;
                break;
              case 'skipped':
                result.skipped++;
                break;
              case 'error':
                result.errors.push(`Error processing: ${piece.content_link}`);
                break;
            }
            
            result.totalProcessed++;
          })
        );
        
        // Log progress
        console.log(`Processed ${Math.min(i + batchSize, contentPieces.length)} / ${contentPieces.length} items`);
      }
      
      console.log('Sync completed:', result);
      return result;
      
    } catch (error) {
      console.error('Sync failed:', error);
      result.errors.push(`Sync failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return result;
    }
  }
}

// Export singleton instance
export const sheetsSyncService = new SheetsSyncService();