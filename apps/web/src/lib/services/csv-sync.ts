import { parse } from 'csv-parse/sync';
import type { ContentPiece } from '../server/supabase';

export interface CsvRow {
  'Client Name': string;
  'Show Topic': string;
  'Show Type': string;
  'Date': string;
  'Show Title': string;
  'Show Link': string;
  'Report Link': string;
  'Views / Listeners': string;
  'Impressions': string;
  'Follow Increase': string;
}

export function extractHostFromUrl(url: string, clientName: string): string {
  try {
    // Extract username from Twitter/X URL
    const match = url.match(/(?:twitter\.com|x\.com)\/(@?[\w]+)(?:\/|$)/);
    if (match && match[1] && match[1] !== 'i') {
      return match[1].replace('@', '').toLowerCase();
    }
  } catch (error) {
    console.warn(`Failed to extract host from URL: ${url}`, error);
  }
  
  // Fallback to sanitized client name
  return clientName.toLowerCase().replace(/\s+/g, '').replace(/[^a-z0-9]/g, '');
}

export function parseNumber(value: string): number {
  if (!value || value === '-' || value === '') return 0;
  
  // Remove ~ prefix if present
  value = value.replace('~', '').trim();
  
  // Handle M (millions)
  if (value.includes('M')) {
    const num = parseFloat(value.replace('M', '').trim());
    return Math.round(num * 1000000);
  }
  
  // Handle K (thousands)
  if (value.toLowerCase().includes('k')) {
    const num = parseFloat(value.replace(/k/i, '').trim());
    return Math.round(num * 1000);
  }
  
  // Handle comma-separated numbers
  value = value.replace(/,/g, '');
  
  const parsed = parseFloat(value);
  return isNaN(parsed) ? 0 : Math.round(parsed);
}

export function parseDate(dateStr: string): string {
  try {
    // Parse DD/MM/YYYY format
    const [day, month, year] = dateStr.split('/');
    const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
    return date.toISOString();
  } catch (error) {
    console.warn(`Failed to parse date: ${dateStr}`, error);
    return new Date().toISOString();
  }
}

// Hardcoded fallback mappings
const FALLBACK_CONTENT_TYPE_MAPPINGS: Record<string, { content_type: string; twitter_content_type: string | null }> = {
  'SS on M': { content_type: 'spotlight_space_mario', twitter_content_type: 'space' },
  'SS on RT': { content_type: 'spotlight_space_roundtable', twitter_content_type: 'space' },
  'Video on M': { content_type: 'video_mario', twitter_content_type: null },
  'Video on RT': { content_type: 'video_roundtable', twitter_content_type: null },
  'CTH': { content_type: 'crypto_townhall', twitter_content_type: null },
  'Countdown': { content_type: 'countdown', twitter_content_type: null },
  '69X Minutes': { content_type: '69x_minutes', twitter_content_type: null }
};

export function mapShowTypeToContentType(showType: string): string[] {
  const normalized = showType.trim();
  
  const mapping = FALLBACK_CONTENT_TYPE_MAPPINGS[normalized];
  return mapping ? [mapping.content_type] : ['marketing'];
}

export function getTwitterContentType(showType: string): string | null {
  const normalized = showType.trim();
  
  const mapping = FALLBACK_CONTENT_TYPE_MAPPINGS[normalized];
  return mapping ? mapping.twitter_content_type : null;
}

export function mapTopicToCategories(topic: string): string[] {
  const normalized = topic.toLowerCase().trim();
  
  const mapping: Record<string, string> = {
    'ai': 'ai',
    'defi': 'defi',
    'gaming': 'gaming',
    'memecoin': 'memecoin',
    'web3': 'web3',
    'web2': 'web2',
    'rwa': 'blockchain',
    'socialfi': 'web3',
    'multi-chain': 'blockchain',
    'countdown': 'news',
    'cth': 'news'
  };
  
  const category = mapping[normalized];
  return category ? [category] : [];
}

export function parseCsvData(csvContent: string): Partial<ContentPiece>[] {
  try {
    const records = parse(csvContent, {
      columns: true,
      skip_empty_lines: true,
      trim: true
    }) as CsvRow[];
    
    return records.map(row => transformCsvRow(row)).filter(Boolean) as Partial<ContentPiece>[];
  } catch (error) {
    console.error('Failed to parse CSV:', error);
    throw new Error('Failed to parse CSV data');
  }
}

export function transformCsvRow(row: CsvRow): Partial<ContentPiece> | null {
  try {
    // Skip rows without a show link
    if (!row['Show Link']) {
      console.warn('Skipping row without Show Link');
      return null;
    }
    
    const host = extractHostFromUrl(row['Show Link'], row['Client Name']);
    
    return {
      content_link: row['Show Link'],
      host,
      content_account: row['Client Name'] ? [row['Client Name']] : [],
      content_created_date: parseDate(row['Date']),
      content_types: mapShowTypeToContentType(row['Show Type']),
      twitter_content_type: getTwitterContentType(row['Show Type']),
      content_categories: mapTopicToCategories(row['Show Topic']),
      content_title: row['Show Title'] || null,
      content_description: row['Show Title'] || null, // Use title as description if not provided
      screenshot_urls: row['Report Link'] ? [row['Report Link']] : [],
      content_views: parseNumber(row['Views / Listeners']),
      twitter_impressions: parseNumber(row['Impressions']),
      content_follow_increase: parseNumber(row['Follow Increase']),
      // Default values
      content_tags: [],
      content_listeners: parseNumber(row['Views / Listeners']), // Same as views for now
      twitter_likes: 0,
      twitter_retweets: 0
    };
  } catch (error) {
    console.error('Failed to transform CSV row:', error, row);
    return null;
  }
}

export async function readCsvFile(filePath: string): Promise<string> {
  const fs = await import('fs/promises');
  return fs.readFile(filePath, 'utf-8');
}