import { SheetsSyncService } from './services/sheets-sync';

async function testFullSync() {
  try {
    console.log('🧪 Testing full sync with Joey.csv...');
    
    // Create sync service instance
    const syncService = new SheetsSyncService();
    
    // Test with dry run first
    console.log('\n--- DRY RUN TEST ---');
    const dryRunResult = await syncService.sync({
      useGoogleSheets: false,
      csvFilePath: '/Users/<USER>/Downloads/Coding/IBC/ibc-final-dashboard/docs/Joey.csv',
      dryRun: true
    });
    
    console.log('Dry run completed:', dryRunResult);
    
    // If dry run looks good, test actual sync (but we'll keep it as dry run for safety)
    console.log('\n--- PARSING TEST (Real Data Processing) ---');
    const realResult = await syncService.sync({
      useGoogleSheets: false,
      csvFilePath: '/Users/<USER>/Downloads/Coding/IBC/ibc-final-dashboard/docs/Joey.csv',
      dryRun: true // Keep as dry run for testing
    });
    
    console.log('✅ Full sync test completed successfully!');
    console.log('Final results:', realResult);
    
  } catch (error) {
    console.error('❌ Sync test failed:', error);
    process.exit(1);
  }
}

// Run the test
testFullSync();