import { createUploadthing, type FileRouter } from "uploadthing/next";
import { UploadThingError } from "uploadthing/server";
import { createServerSupabaseClient } from "../server/supabase";

const f = createUploadthing();

// Admin authentication function - only admins can upload
const authenticateAdmin = async (req: Request) => {
  try {
    const supabase = await createServerSupabaseClient();
    
    // Get the current user from the session
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      console.log("❌ [UploadThing Auth] No authenticated user:", userError?.message);
      return null;
    }
    
    // Check if user has admin role
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('role')
      .eq('user_id', user.id)
      .single();
    
    if (profileError || !profile) {
      console.log("❌ [UploadThing Auth] Failed to get user profile:", profileError?.message);
      return null;
    }
    
    if (profile.role !== 'admin') {
      console.log("❌ [UploadThing Auth] User is not admin:", { userId: user.id, role: profile.role });
      return null;
    }
    
    console.log("✅ [UploadThing Auth] Admin authenticated:", { userId: user.id, email: user.email });
    return { id: user.id, email: user.email, role: profile.role };
    
  } catch (error) {
    console.error("❌ [UploadThing Auth] Authentication error:", error);
    return null;
  }
};

// FileRouter for your app
export const ourFileRouter = {
  // Route for testimonial screenshot uploads
  testimonialScreenshots: f({
    image: {
      maxFileSize: "8MB", // Large enough for high-quality screenshots
      maxFileCount: 5, // Allow multiple screenshots per testimonial
    },
  })
    .middleware(async ({ req }) => {
      // This code runs on your server before upload
      const adminUser = await authenticateAdmin(req);

      // Only allow uploads if user is authenticated admin
      if (!adminUser) {
        throw new UploadThingError("Unauthorized: Admin access required for file uploads");
      }

      // Whatever is returned here is accessible in onUploadComplete as `metadata`
      return { 
        userId: adminUser.id, 
        userEmail: adminUser.email, 
        uploadedBy: adminUser.role 
      };
    })
    .onUploadComplete(async ({ metadata, file }) => {
      // This code RUNS ON YOUR SERVER after upload
      console.log("✅ [UploadThing] Upload complete for admin:", {
        userId: metadata.userId,
        userEmail: metadata.userEmail,
        fileUrl: file.url,
        fileName: file.name,
        fileSize: file.size
      });

      // Log successful upload for audit trail
      console.log(`📷 [UploadThing] Testimonial screenshot uploaded by admin ${metadata.userEmail}: ${file.name} (${file.size} bytes)`);

      // Return data to the clientside `onClientUploadComplete` callback
      return { 
        uploadedBy: metadata.uploadedBy,
        adminEmail: metadata.userEmail,
        fileUrl: file.url,
        fileName: file.name,
        fileSize: file.size
      };
    }),

  // You can add more upload routes here for different purposes
  // For example, profile pictures, document uploads, etc.
} satisfies FileRouter;

export type OurFileRouter = typeof ourFileRouter;