import {
  generateUploadButton,
  generateUploadDropzone,
  generateReactHelpers,
} from "@uploadthing/react";

import type { OurFileRouter } from "./core";

// Generate typed components for our file router
export const UploadButton = generateUploadButton<OurFileRouter>();
export const UploadDropzone = generateUploadDropzone<OurFileRouter>();

// Generate React hooks for programmatic uploads
export const { useUploadThing, uploadFiles } = generateReactHelpers<OurFileRouter>();