/**
 * tRPC middleware for rate limiting
 */

import { TRPCError } from '@trpc/server';
import { t } from '../trpc';
import { rateLimiter, getClientIdentifier, getRuleType } from '../../security/rate-limiting';
import { securityLog } from '../../security/logging';

/**
 * Rate limiting middleware for tRPC procedures
 */
export const rateLimitMiddleware = t.middleware(async ({ ctx, path, type, next }) => {
  // Get client identifier (prefer user ID if authenticated)
  const clientId = getClientIdentifier(
    { headers: {} }, // Headers not available in tRPC context
    ctx.user?.id
  );

  // Determine rate limit rule based on operation type and path
  const ruleType = getRuleType(type, path);

  // Check rate limit
  const result = rateLimiter.checkLimit(clientId, ruleType);

  // Log security events for blocked requests
  if (!result.allowed) {
    securityLog('Rate limit exceeded', {
      clientId,
      ruleType,
      path,
      type,
      blocked: result.blocked,
      remaining: result.remaining,
    });
  }

  if (!result.allowed) {
    throw new TRPCError({
      code: 'TOO_MANY_REQUESTS',
      message: result.blocked 
        ? `Too many requests. You are temporarily blocked. Try again after ${new Date(result.resetTime).toISOString()}`
        : `Rate limit exceeded. Try again after ${new Date(result.resetTime).toISOString()}`,
    });
  }

  // Rate limit headers would be added at HTTP middleware level, not here in tRPC

  return next();
});

/**
 * Stricter rate limiting for authentication endpoints
 */
export const authRateLimitMiddleware = t.middleware(async ({ ctx, path, next }) => {
  const clientId = getClientIdentifier(
    { headers: {} }, // Headers not available in tRPC context
    ctx.user?.id
  );

  const result = rateLimiter.checkLimit(clientId, 'auth');

  if (!result.allowed) {
    securityLog('Authentication rate limit exceeded', {
      clientId,
      path,
      blocked: result.blocked,
    });

    throw new TRPCError({
      code: 'TOO_MANY_REQUESTS',
      message: 'Too many authentication attempts. Please wait before trying again.',
    });
  }

  // Rate limit headers would be added at HTTP middleware level

  return next();
});

/**
 * Admin operation rate limiting
 */
export const adminRateLimitMiddleware = t.middleware(async ({ ctx, path, next }) => {
  const clientId = getClientIdentifier(
    { headers: {} }, // Headers not available in tRPC context
    ctx.user?.id
  );

  const result = rateLimiter.checkLimit(clientId, 'admin');

  if (!result.allowed) {
    securityLog('Admin rate limit exceeded', {
      clientId,
      path,
      userId: ctx.user?.id,
    });

    throw new TRPCError({
      code: 'TOO_MANY_REQUESTS',
      message: 'Too many admin operations. Please wait before continuing.',
    });
  }

  // Rate limit headers would be added at HTTP middleware level

  return next();
});

/**
 * Create rate-limited procedure types
 */
export const rateLimitedProcedure = t.procedure.use(rateLimitMiddleware);
export const authRateLimitedProcedure = t.procedure.use(authRateLimitMiddleware);
export const adminRateLimitedProcedure = t.procedure.use(adminRateLimitMiddleware);