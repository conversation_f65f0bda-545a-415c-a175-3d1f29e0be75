import {
  publicProcedure,
  protectedProcedure,
  router,
} from "../trpc";
import { createServerSupabaseClient, type Group, type GroupContent, type GroupWithContent } from "../supabase";
import { z } from "zod";

export const groupsRouter = router({
  // Create a new group
  createGroup: protectedProcedure
    .input(z.object({
      name: z.string().min(1).max(100),
      description: z.string().max(500).optional(),
      is_public: z.boolean().default(true)
    }))
    .mutation(async ({ input, ctx }) => {
      const supabase = await createServerSupabaseClient();
      
      const { data, error } = await supabase
        .from('groups')
        .insert([{
          name: input.name,
          description: input.description || null,
          is_public: input.is_public,
          user_id: ctx.user.id
        }])
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to create group: ${error.message}`);
      }

      return data as Group;
    }),

  // Get all user's groups
  getGroups: protectedProcedure
    .input(z.object({
      limit: z.number().default(50)
    }))
    .query(async ({ input, ctx }) => {
      const supabase = await createServerSupabaseClient();
      
      const { data, error } = await supabase
        .from('groups')
        .select(`
          *,
          group_content(count)
        `)
        .eq('user_id', ctx.user.id)
        .order('created_at', { ascending: false })
        .limit(input.limit);

      if (error) {
        throw new Error(`Failed to fetch groups: ${error.message}`);
      }

      return data?.map(group => ({
        ...group,
        content_count: group.group_content?.[0]?.count || 0
      })) as (Group & { content_count: number })[];
    }),

  // Get single group with content
  getGroup: protectedProcedure
    .input(z.object({
      id: z.string().uuid(),
      limit: z.number().default(50)
    }))
    .query(async ({ input, ctx }) => {
      const supabase = await createServerSupabaseClient();
      
      // First get the group - allow access if user owns it OR if it's public
      const { data: group, error: groupError } = await supabase
        .from('groups')
        .select('*')
        .eq('id', input.id)
        .or(`user_id.eq.${ctx.user.id},is_public.eq.true`)
        .single();

      if (groupError) {
        throw new Error(`Failed to fetch group: ${groupError.message}`);
      }

      // Then get the content with join
      const { data: content, error: contentError } = await supabase
        .from('group_content')
        .select(`
          position,
          added_at,
          content_pieces (*)
        `)
        .eq('group_id', input.id)
        .order('position', { ascending: true })
        .limit(input.limit);

      if (contentError) {
        throw new Error(`Failed to fetch group content: ${contentError.message}`);
      }

      const groupWithContent: GroupWithContent = {
        ...group,
        content: content?.map((item: any) => ({
          ...item.content_pieces,
          position: item.position,
          added_at: item.added_at
        })) || [],
        content_count: content?.length || 0
      };

      return groupWithContent;
    }),

  // Get public group with content (unauthenticated access)
  getPublicGroup: publicProcedure
    .input(z.object({
      id: z.string().uuid(),
      limit: z.number().default(50)
    }))
    .query(async ({ input }) => {
      // Use basic supabase client for public access without authentication
      const { supabase } = await import("../supabase");
      
      console.log(`🔍 [getPublicGroup] Searching for public group with ID: ${input.id}`);
      
      // Get public group only - use .maybeSingle() to handle potential no-results case
      const { data: group, error: groupError } = await supabase
        .from('groups')
        .select('*')
        .eq('id', input.id)
        .eq('is_public', true)
        .maybeSingle();

      console.log(`🔍 [getPublicGroup] Query result:`, { group, groupError });

      if (groupError) {
        console.error(`❌ [getPublicGroup] Database error:`, groupError);
        throw new Error(`Failed to fetch public group: ${groupError.message}`);
      }

      if (!group) {
        console.log(`❌ [getPublicGroup] Group not found or not public: ${input.id}`);
        throw new Error(`Public group not found`);
      }

      // Get the content with join
      console.log(`🔍 [getPublicGroup] Fetching content for group: ${input.id}`);
      const { data: content, error: contentError } = await supabase
        .from('group_content')
        .select(`
          position,
          added_at,
          content_pieces (*)
        `)
        .eq('group_id', input.id)
        .order('position', { ascending: true })
        .limit(input.limit);

      console.log(`🔍 [getPublicGroup] Content query result:`, { content, contentError });

      if (contentError) {
        console.error(`❌ [getPublicGroup] Content fetch error:`, contentError);
        throw new Error(`Failed to fetch group content: ${contentError.message}`);
      }

      const groupWithContent: GroupWithContent = {
        ...group,
        content: content?.map((item: any) => ({
          ...item.content_pieces,
          position: item.position,
          added_at: item.added_at
        })) || [],
        content_count: content?.length || 0
      };

      console.log(`✅ [getPublicGroup] Successfully retrieved public group:`, {
        groupId: group.id,
        groupName: group.name,
        contentCount: groupWithContent.content_count
      });

      return groupWithContent;
    }),

  // Update group
  updateGroup: protectedProcedure
    .input(z.object({
      id: z.string().uuid(),
      name: z.string().min(1).max(100).optional(),
      description: z.string().max(500).optional(),
      is_public: z.boolean().optional()
    }))
    .mutation(async ({ input, ctx }) => {
      const supabase = await createServerSupabaseClient();
      const { id, ...updates } = input;
      
      const { data, error } = await supabase
        .from('groups')
        .update(updates)
        .eq('id', id)
        .eq('user_id', ctx.user.id)
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to update group: ${error.message}`);
      }

      return data as Group;
    }),

  // Get shareable link for a group
  getGroupShareLink: protectedProcedure
    .input(z.object({
      id: z.string().uuid()
    }))
    .query(async ({ input, ctx }) => {
      const supabase = await createServerSupabaseClient();
      
      // Verify user owns the group
      const { data: group, error } = await supabase
        .from('groups')
        .select('id, name, is_public')
        .eq('id', input.id)
        .eq('user_id', ctx.user.id)
        .single();

      if (error || !group) {
        throw new Error('Group not found or access denied');
      }

      if (!group.is_public) {
        throw new Error('Only public groups can be shared');
      }

      const baseUrl = process.env.CORS_ORIGIN || 'http://localhost:3001';
      const shareUrl = `${baseUrl}/groups/share/${group.id}`;

      return {
        shareUrl,
        groupName: group.name,
        isPublic: group.is_public
      };
    }),

  // Get public groups for discovery
  getPublicGroups: publicProcedure
    .input(z.object({
      limit: z.number().default(20),
      search: z.string().optional()
    }))
    .query(async ({ input }) => {
      // Use basic supabase client for public access without authentication
      const { supabase } = await import("../supabase");
      
      let query = supabase
        .from('groups')
        .select(`
          *,
          group_content(count)
        `)
        .eq('is_public', true)
        .order('created_at', { ascending: false })
        .limit(input.limit);

      // Add search filter if provided
      if (input.search && input.search.length > 0) {
        query = query.or(`name.ilike.%${input.search}%,description.ilike.%${input.search}%`);
      }

      const { data, error } = await query;

      if (error) {
        throw new Error(`Failed to fetch public groups: ${error.message}`);
      }

      return data?.map(group => ({
        ...group,
        content_count: group.group_content?.[0]?.count || 0
      })) as (Group & { content_count: number })[];
    }),

  // Delete group
  deleteGroup: protectedProcedure
    .input(z.object({
      id: z.string().uuid()
    }))
    .mutation(async ({ input, ctx }) => {
      const supabase = await createServerSupabaseClient();
      
      const { error } = await supabase
        .from('groups')
        .delete()
        .eq('id', input.id)
        .eq('user_id', ctx.user.id);

      if (error) {
        throw new Error(`Failed to delete group: ${error.message}`);
      }

      return { success: true };
    }),

  // Add content to group
  addContentToGroup: protectedProcedure
    .input(z.object({
      group_id: z.string().uuid(),
      content_id: z.number(),
      position: z.number().optional()
    }))
    .mutation(async ({ input, ctx }) => {
      const supabase = await createServerSupabaseClient();
      
      // Verify user owns the group
      const { data: group, error: groupError } = await supabase
        .from('groups')
        .select('id')
        .eq('id', input.group_id)
        .eq('user_id', ctx.user.id)
        .single();

      if (groupError || !group) {
        throw new Error('Group not found or access denied');
      }

      // Get the next position if not provided
      let position = input.position;
      if (position === undefined) {
        const { data: maxPos } = await supabase
          .from('group_content')
          .select('position')
          .eq('group_id', input.group_id)
          .order('position', { ascending: false })
          .limit(1);
        
        position = (maxPos?.[0]?.position ?? -1) + 1;
      }

      const { data, error } = await supabase
        .from('group_content')
        .insert([{
          group_id: input.group_id,
          content_id: input.content_id,
          position
        }])
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to add content to group: ${error.message}`);
      }

      return data as GroupContent;
    }),

  // Remove content from group
  removeContentFromGroup: protectedProcedure
    .input(z.object({
      group_id: z.string().uuid(),
      content_id: z.number()
    }))
    .mutation(async ({ input, ctx }) => {
      const supabase = await createServerSupabaseClient();
      
      // Verify user owns the group through the RLS policy
      const { error } = await supabase
        .from('group_content')
        .delete()
        .eq('group_id', input.group_id)
        .eq('content_id', input.content_id);

      if (error) {
        throw new Error(`Failed to remove content from group: ${error.message}`);
      }

      return { success: true };
    }),

  // Reorder content in group
  reorderGroupContent: protectedProcedure
    .input(z.object({
      group_id: z.string().uuid(),
      content_positions: z.array(z.object({
        content_id: z.number(),
        position: z.number()
      }))
    }))
    .mutation(async ({ input, ctx }) => {
      const supabase = await createServerSupabaseClient();
      
      // Update positions for each content item
      const updates = input.content_positions.map(async ({ content_id, position }) => {
        return supabase
          .from('group_content')
          .update({ position })
          .eq('group_id', input.group_id)
          .eq('content_id', content_id);
      });

      const results = await Promise.all(updates);
      
      // Check for any errors
      const hasError = results.some(result => result.error);
      if (hasError) {
        throw new Error('Failed to reorder content');
      }

      return { success: true };
    }),

  // Check if content is in any user groups (for UI state)
  getContentGroupStatus: protectedProcedure
    .input(z.object({
      content_id: z.number()
    }))
    .query(async ({ input, ctx }) => {
      const supabase = await createServerSupabaseClient();
      
      const { data, error } = await supabase
        .from('group_content')
        .select(`
          group_id,
          groups!inner (
            id,
            name,
            user_id
          )
        `)
        .eq('content_id', input.content_id)
        .eq('groups.user_id', ctx.user.id);

      if (error) {
        throw new Error(`Failed to check content group status: ${error.message}`);
      }

      return data?.map((item: any) => ({
        group_id: item.group_id,
        group_name: item.groups.name
      })) || [];
    }),
});