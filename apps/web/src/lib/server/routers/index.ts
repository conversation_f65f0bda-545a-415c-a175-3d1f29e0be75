import {
  publicProcedure,
  router,
} from "../trpc";
import { authRouter } from "./auth";
import { contentRouter } from "./content";
import { groupsRouter } from "./groups";
import { statsRouter } from "./stats";
import { mediaMetricsRouter } from "./mediaMetrics";

export const appRouter = router({
  healthCheck: publicProcedure.query(() => {
    return "OK";
  }),

  // Mount sub-routers
  auth: authRouter,
  content: contentRouter,
  groups: groupsRouter,
  stats: statsRouter,
  mediaMetrics: mediaMetricsRouter,
});

export type AppRouter = typeof appRouter;