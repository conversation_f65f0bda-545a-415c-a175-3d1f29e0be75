import {
  publicProcedure,
  adminProcedure,
  router,
} from "../trpc";
import { supabase, createServerSupabaseClient } from "../supabase";
import { z } from "zod";

// Helper: remove only undefined values to avoid overwriting with nulls unintentionally
const omitUndefined = <T extends Record<string, any>>(obj: T): Partial<T> => {
  const entries = Object.entries(obj).filter(([, v]) => v !== undefined);
  return Object.fromEntries(entries) as Partial<T>;
};

const MediaMetricSchema = z.object({
  id: z.string().uuid().optional(),
  account_handle: z.string().min(1),
  display_name: z.string().min(1),
  description: z.string().nullable().optional(),
  profile_image_url: z.string().url().nullable().optional(),
  total_impressions: z.number().min(0),
  total_views: z.number().min(0).optional(),
  total_followers: z.number().min(0).optional(),
  is_active: z.boolean().optional(),
  display_order: z.number().min(0).optional(),
});

const UpdateMediaMetricSchema = MediaMetricSchema.extend({
  id: z.string().uuid(),
});

export const mediaMetricsRouter = router({
  // Get all active media account metrics (public endpoint)
  getMediaMetrics: publicProcedure.query(async () => {
    const { data, error } = await supabase
      .from('media_account_metrics')
      .select('*')
      .eq('is_active', true)
      .order('display_order', { ascending: true });

    if (error) {
      throw new Error(`Failed to fetch media metrics: ${error.message}`);
    }

    return data || [];
  }),

  // Get all media account metrics for admin (includes inactive)
  getAllMediaMetrics: adminProcedure.query(async () => {
    const sb = await createServerSupabaseClient();
    const { data, error } = await sb
      .from('media_account_metrics')
      .select('*')
      .order('display_order', { ascending: true });

    if (error) {
      console.error('❌ [MEDIA_METRICS] Failed to fetch all media metrics', { error });
      throw new Error(`Failed to fetch all media metrics: ${error.message}`);
    }

    return data || [];
  }),

  // Get metrics by account handles
  getMetricsByHandles: publicProcedure
    .input(z.object({ handles: z.array(z.string()) }))
    .query(async ({ input }) => {
      const { data, error } = await supabase
        .from('media_account_metrics')
        .select('*')
        .in('account_handle', input.handles)
        .eq('is_active', true);

      if (error) {
        throw new Error(`Failed to fetch metrics by handles: ${error.message}`);
      }

      // Convert to object keyed by handle for easier access
      const metricsMap = (data || []).reduce((acc, metric) => {
        acc[metric.account_handle] = metric;
        return acc;
      }, {} as Record<string, any>);

      return metricsMap;
    }),

  // Create new media account metric
  createMediaMetric: adminProcedure
    .input(MediaMetricSchema.omit({ id: true }))
    .mutation(async ({ input, ctx }) => {
      const payload = { ...omitUndefined(input), created_by: ctx.user?.id } as Record<string, any>;
      console.info('🔧 [MEDIA_METRICS] Create metric requested', { payload });

      const sb = await createServerSupabaseClient();
      const { data, error } = await sb
        .from('media_account_metrics')
        .insert(payload)
        .select()
        .maybeSingle();

      if (error) {
        console.error('❌ [MEDIA_METRICS] Failed to create media metric', { payload, error });
        // If insert succeeded but SELECT is blocked by RLS, PostgREST can return PGRST116 via .single()
        // We used maybeSingle() to avoid throwing for 0 rows; still surface real errors.
        throw new Error(`Failed to create media metric: ${error.message}`);
      }

      if (!data) {
        console.warn('⚠️ [MEDIA_METRICS] Create returned no row (likely SELECT blocked by RLS). Returning minimal payload.');
        return payload;
      }

      return data;
    }),

  // Update existing media account metric
  updateMediaMetric: adminProcedure
    .input(UpdateMediaMetricSchema)
    .mutation(async ({ input }) => {
      const { id, ...updateDataRaw } = input;
      const updateData = omitUndefined(updateDataRaw);
      console.info('🔧 [MEDIA_METRICS] Update requested', { id, updateKeys: Object.keys(updateData) });

      // Pre-check existence to distinguish between not-found vs RLS-select issues
      const sb = await createServerSupabaseClient();
      const { data: exists, error: existsError } = await sb
        .from('media_account_metrics')
        .select('id')
        .eq('id', id)
        .maybeSingle();

      if (existsError) {
        console.error('❌ [MEDIA_METRICS] Existence check failed before update', { id, error: existsError });
        throw new Error(`Failed to check media metric: ${existsError.message}`);
      }
      if (!exists) {
        console.warn('⚠️ [MEDIA_METRICS] Media metric not found for update', { id });
        throw new Error('Media metric not found');
      }

      const { data, error } = await sb
        .from('media_account_metrics')
        .update(updateData)
        .eq('id', id)
        .select()
        .maybeSingle();

      // Handle real errors (not the empty row case)
      if (error) {
        const msg = error.message || '';
        const isSingleRowError = msg.includes('JSON object requested, multiple (or no) rows returned');
        if (isSingleRowError) {
          console.warn('⚠️ [MEDIA_METRICS] Update succeeded but SELECT returned 0 rows (likely RLS). Returning minimal object.', { id });
          return { id, ...updateData } as any;
        }
        console.error('❌ [MEDIA_METRICS] Failed to update media metric', { id, updateData, error });
        throw new Error(`Failed to update media metric: ${msg}`);
      }

      if (!data) {
        // No data returned (SELECT blocked or no returning). Assume success as we pre-checked existence
        console.warn('⚠️ [MEDIA_METRICS] Update returned no row (likely SELECT blocked by RLS). Returning minimal object.', { id });
        return { id, ...updateData } as any;
      }

      return data;
    }),

  // Update multiple metrics at once (for bulk updates)
  updateMultipleMetrics: adminProcedure
    .input(z.array(UpdateMediaMetricSchema))
    .mutation(async ({ input }) => {
      const results: any[] = [];

      for (const metric of input) {
        const { id, ...updateDataRaw } = metric;
        const updateData = omitUndefined(updateDataRaw);
        console.info('🔧 [MEDIA_METRICS] Bulk update requested', { id, updateKeys: Object.keys(updateData) });

        const sb = await createServerSupabaseClient();
        const { data: exists, error: existsError } = await sb
          .from('media_account_metrics')
          .select('id')
          .eq('id', id)
          .maybeSingle();

        if (existsError) {
          console.error('❌ [MEDIA_METRICS] Existence check failed in bulk update', { id, error: existsError });
          throw new Error(`Failed to check metric ${id}: ${existsError.message}`);
        }
        if (!exists) {
          console.warn('⚠️ [MEDIA_METRICS] Metric not found in bulk update', { id });
          continue; // skip missing IDs
        }

        const { data, error } = await sb
          .from('media_account_metrics')
          .update(updateData)
          .eq('id', id)
          .select()
          .maybeSingle();

        if (error) {
          const msg = error.message || '';
          const isSingleRowError = msg.includes('JSON object requested, multiple (or no) rows returned');
          if (isSingleRowError) {
            console.warn('⚠️ [MEDIA_METRICS] Bulk update SELECT returned 0 rows (likely RLS). Returning minimal object.', { id });
            results.push({ id, ...updateData });
            continue;
          }
          console.error('❌ [MEDIA_METRICS] Failed to update metric in bulk', { id, updateData, error });
          throw new Error(`Failed to update metric ${id}: ${msg}`);
        }

        results.push(data ?? { id, ...updateData });
      }

      return results;
    }),

  // Delete media account metric
  deleteMediaMetric: adminProcedure
    .input(z.object({ id: z.string().uuid() }))
    .mutation(async ({ input }) => {
      console.info('🗑️ [MEDIA_METRICS] Delete requested', { id: input.id });
      const sb = await createServerSupabaseClient();
      const { error } = await sb
        .from('media_account_metrics')
        .delete()
        .eq('id', input.id);

      if (error) {
        console.error('❌ [MEDIA_METRICS] Failed to delete media metric', { id: input.id, error });
        throw new Error(`Failed to delete media metric: ${error.message}`);
      }

      return { success: true };
    }),

  // Toggle active status
  toggleActive: adminProcedure
    .input(z.object({
      id: z.string().uuid(),
      is_active: z.boolean()
    }))
    .mutation(async ({ input }) => {
      console.info('🔧 [MEDIA_METRICS] Toggle active requested', { id: input.id, is_active: input.is_active });
      const sb = await createServerSupabaseClient();
      const { data, error } = await sb
        .from('media_account_metrics')
        .update({ is_active: input.is_active })
        .eq('id', input.id)
        .select()
        .maybeSingle();

      if (error) {
        const msg = error.message || '';
        const isSingleRowError = msg.includes('JSON object requested, multiple (or no) rows returned');
        if (isSingleRowError) {
          console.warn('⚠️ [MEDIA_METRICS] Toggle active updated but SELECT returned 0 rows (likely RLS). Returning minimal object.', { id: input.id });
          return { id: input.id, is_active: input.is_active } as any;
        }
        console.error('❌ [MEDIA_METRICS] Failed to toggle active status', { id: input.id, error });
        throw new Error(`Failed to toggle active status: ${msg}`);
      }

      return data ?? ({ id: input.id, is_active: input.is_active } as any);
    }),

  // Update display order
  updateDisplayOrder: adminProcedure
    .input(z.array(z.object({
      id: z.string().uuid(),
      display_order: z.number().min(0)
    })))
    .mutation(async ({ input }) => {
      const results = [];

      for (const item of input) {
        const sb = await createServerSupabaseClient();
        const { data, error } = await sb
          .from('media_account_metrics')
          .update({ display_order: item.display_order })
          .eq('id', item.id)
          .select()
          .maybeSingle();

        if (error) {
          const msg = error.message || '';
          const isSingleRowError = msg.includes('JSON object requested, multiple (or no) rows returned');
          if (isSingleRowError) {
            console.warn('⚠️ [MEDIA_METRICS] Update display_order SELECT returned 0 rows (likely RLS). Returning minimal object.', { id: item.id });
            results.push({ id: item.id, display_order: item.display_order });
            continue;
          }
          console.error('❌ [MEDIA_METRICS] Failed to update display order', { id: item.id, error });
          throw new Error(`Failed to update display order for ${item.id}: ${msg}`);
        }

        results.push(data ?? { id: item.id, display_order: item.display_order });
      }

      return results;
    }),
});