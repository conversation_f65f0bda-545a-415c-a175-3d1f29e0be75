import {
  adminProcedure,
  router,
} from "../../trpc";
import { supabase } from "../../supabase";
import { z } from "zod";

// Type for RPC response validation
interface FilterMetadataItem {
  type: string;
  value: string;
  usage_count: number;
}

// Validate RPC response
function validateFilterMetadata(data: unknown): FilterMetadataItem[] {
  if (!Array.isArray(data)) {
    throw new Error('Invalid response: expected array');
  }
  
  return data.map((item, index) => {
    if (typeof item !== 'object' || item === null) {
      throw new Error(`Invalid response at index ${index}: expected object`);
    }
    
    const obj = item as Record<string, unknown>;
    
    if (typeof obj.type !== 'string' || 
        typeof obj.value !== 'string' || 
        typeof obj.usage_count !== 'number') {
      throw new Error(`Invalid response at index ${index}: missing or invalid fields`);
    }
    
    return {
      type: obj.type,
      value: obj.value,
      usage_count: obj.usage_count
    };
  });
}

export const metadataRouter = router({
  // Get all filter metadata with usage counts
  getFilterMetadata: adminProcedure
    .input(z.object({
      type: z.enum(['content_tags', 'content_account', 'content_types', 'twitter_content_type', 'content_categories']).optional()
    }))
    .query(async ({ input }) => {
      const { data, error } = await supabase.rpc('get_filter_metadata', {
        filter_type: input.type || null
      });

      if (error) {
        throw new Error(`Failed to fetch filter metadata: ${error.message}`);
      }

      // Validate and group the results by type
      const validatedData = validateFilterMetadata(data);
      const grouped = validatedData.reduce((acc, item) => {
        if (!acc[item.type]) {
          acc[item.type] = [];
        }
        acc[item.type].push({
          value: item.value,
          usageCount: item.usage_count
        });
        return acc;
      }, {} as Record<string, Array<{value: string, usageCount: number}>>);

      return grouped;
    }),

  // Rename a filter value across all content
  renameFilterValue: adminProcedure
    .input(z.object({
      type: z.enum(['content_tags', 'content_account', 'content_types', 'twitter_content_type', 'content_categories']),
      oldValue: z.string(),
      newValue: z.string()
    }))
    .mutation(async ({ input }) => {
      const { type, oldValue, newValue } = input;
      
      let updateQuery;
      switch (type) {
        case 'content_account':
          // For array fields, we need to use PostgreSQL array functions
          const { data: accountData, error: accountError } = await supabase.rpc('rename_array_value', {
            p_table_name: 'content_pieces',
            p_column_name: 'content_account',
            p_old_value: oldValue,
            p_new_value: newValue
          });
          if (accountError) throw new Error(`Failed to rename content account: ${accountError.message}`);
          return { success: true, updatedCount: accountData };
        case 'twitter_content_type':
          // Validate that newValue is a valid twitter_content_type enum value
          const validTwitterContentTypes = ['space', 'interview', 'tweet', 'thread', 'retweet'] as const;
          type TwitterContentType = typeof validTwitterContentTypes[number];
          
          if (!validTwitterContentTypes.includes(newValue as TwitterContentType)) {
            throw new Error(`Invalid twitter_content_type: ${newValue}. Must be one of: ${validTwitterContentTypes.join(', ')}`);
          }
          
          updateQuery = supabase
            .from('content_pieces')
            .update({ twitter_content_type: newValue as TwitterContentType })
            .eq('twitter_content_type', oldValue);
          break;
        case 'content_tags':
          // For array fields, we need to use PostgreSQL array functions
          const { data: tagData, error: tagError } = await supabase.rpc('rename_array_value', {
            p_table_name: 'content_pieces',
            p_column_name: 'content_tags',
            p_old_value: oldValue,
            p_new_value: newValue
          });
          if (tagError) throw new Error(`Failed to rename content tag: ${tagError.message}`);
          return { success: true, updatedCount: tagData };
        case 'content_types':
          const { data: typeData, error: typeError } = await supabase.rpc('rename_array_value', {
            p_table_name: 'content_pieces',
            p_column_name: 'content_types',
            p_old_value: oldValue,
            p_new_value: newValue
          });
          if (typeError) throw new Error(`Failed to rename content type: ${typeError.message}`);
          return { success: true, updatedCount: typeData };
        case 'content_categories':
          const { data: catData, error: catError } = await supabase.rpc('rename_array_value', {
            p_table_name: 'content_pieces',
            p_column_name: 'content_categories',
            p_old_value: oldValue,
            p_new_value: newValue
          });
          if (catError) throw new Error(`Failed to rename content category: ${catError.message}`);
          return { success: true, updatedCount: catData };
      }

      // This block only executes for twitter_content_type
      if (updateQuery) {
        const { data, error } = await updateQuery.select('id');
        if (error) {
          throw new Error(`Failed to rename ${type}: ${error.message}`);
        }
        return { success: true, updatedCount: data?.length || 0 };
      }
      
      // Should never reach here due to exhaustive switch
      throw new Error(`Unhandled type: ${type}`);
    }),

  // Merge multiple filter values into one
  mergeFilterValues: adminProcedure
    .input(z.object({
      type: z.enum(['content_tags', 'content_account', 'content_types', 'twitter_content_type', 'content_categories']),
      sourceValues: z.array(z.string()),
      targetValue: z.string()
    }))
    .mutation(async ({ input }) => {
      const { type, sourceValues, targetValue } = input;
      let totalUpdated = 0;

      for (const sourceValue of sourceValues) {
        if (sourceValue === targetValue) continue; // Skip if already the target value

        let updateQuery;
        switch (type) {
          case 'content_account':
            const { data: accountData, error: accountError } = await supabase.rpc('rename_array_value', {
              p_table_name: 'content_pieces',
              p_column_name: 'content_account',
              p_old_value: sourceValue,
              p_new_value: targetValue
            });
            if (accountError) throw new Error(`Failed to merge content account: ${accountError.message}`);
            totalUpdated += accountData || 0;
            continue;
          case 'twitter_content_type':
            // Validate that targetValue is a valid twitter_content_type enum value
            const validTwitterContentTypes = ['space', 'interview', 'tweet', 'thread', 'retweet'] as const;
            type TwitterContentType = typeof validTwitterContentTypes[number];
            
            if (!validTwitterContentTypes.includes(targetValue as TwitterContentType)) {
              throw new Error(`Invalid twitter_content_type: ${targetValue}. Must be one of: ${validTwitterContentTypes.join(', ')}`);
            }
            
            updateQuery = supabase
              .from('content_pieces')
              .update({ twitter_content_type: targetValue as TwitterContentType })
              .eq('twitter_content_type', sourceValue);
            break;
          case 'content_tags':
            const { data: tagData, error: tagError } = await supabase.rpc('rename_array_value', {
              p_table_name: 'content_pieces',
              p_column_name: 'content_tags',
              p_old_value: sourceValue,
              p_new_value: targetValue
            });
            if (tagError) throw new Error(`Failed to merge content tag: ${tagError.message}`);
            totalUpdated += tagData || 0;
            continue;
          case 'content_types':
            const { data: typeData, error: typeError } = await supabase.rpc('rename_array_value', {
              p_table_name: 'content_pieces',
              p_column_name: 'content_types',
              p_old_value: sourceValue,
              p_new_value: targetValue
            });
            if (typeError) throw new Error(`Failed to merge content type: ${typeError.message}`);
            totalUpdated += typeData || 0;
            continue;
          case 'content_categories':
            const { data: catData, error: catError } = await supabase.rpc('rename_array_value', {
              p_table_name: 'content_pieces',
              p_column_name: 'content_categories',
              p_old_value: sourceValue,
              p_new_value: targetValue
            });
            if (catError) throw new Error(`Failed to merge content category: ${catError.message}`);
            totalUpdated += catData || 0;
            continue;
        }

        // This block only executes for twitter_content_type
        if (updateQuery) {
          const { data, error } = await updateQuery.select('id');
          if (error) {
            throw new Error(`Failed to merge ${type}: ${error.message}`);
          }
          totalUpdated += data?.length || 0;
        }
      }

      return { success: true, updatedCount: totalUpdated };
    }),

  // Check usage of filter values without deleting
  checkFilterValueUsage: adminProcedure
    .input(z.object({
      type: z.enum(['content_tags', 'content_account', 'content_types', 'twitter_content_type', 'content_categories']),
      value: z.string()
    }))
    .mutation(async ({ input }) => {
      const { type, value } = input;

      // First check usage count
      const { data: usage, error: usageError } = await supabase.rpc('get_value_usage_count', {
        filter_type: type,
        filter_value: value
      });

      if (usageError) {
        throw new Error(`Failed to check usage count: ${usageError.message}`);
      }

      return { 
        success: true, 
        usageCount: usage || 0,
        message: usage > 0 
          ? `Value "${value}" is used in ${usage} content pieces` 
          : `Value "${value}" is not currently in use`
      };
    }),

  // Add new content type to the content_type enum
  addContentType: adminProcedure
    .input(z.object({
      value: z.string().min(1).max(50),
    }))
    .mutation(async ({ input }) => {
      const { value } = input;
      
      // Add the new value to the content_type enum
      const { error } = await supabase.rpc('add_enum_value', {
        enum_name: 'content_type',
        new_value: value.toLowerCase()
      });

      if (error) {
        throw new Error(`Failed to add content type: ${error.message}`);
      }

      return { 
        success: true, 
        message: `Content type "${value}" added successfully` 
      };
    }),

  // Add new content category to the content_category enum  
  addContentCategory: adminProcedure
    .input(z.object({
      value: z.string().min(1).max(50),
    }))
    .mutation(async ({ input }) => {
      const { value } = input;
      
      // Add the new value to the content_category enum
      const { error } = await supabase.rpc('add_enum_value', {
        enum_name: 'content_category',
        new_value: value.toLowerCase()
      });

      if (error) {
        throw new Error(`Failed to add content category: ${error.message}`);
      }

      return { 
        success: true, 
        message: `Content category "${value}" added successfully` 
      };
    }),

  // Add new Twitter content type to the twitter_content_type enum
  addTwitterContentType: adminProcedure
    .input(z.object({
      value: z.string().min(1).max(50),
    }))
    .mutation(async ({ input }) => {
      const { value } = input;
      
      // Add the new value to the twitter_content_type enum
      const { error } = await supabase.rpc('add_enum_value', {
        enum_name: 'twitter_content_type',
        new_value: value.toLowerCase()
      });

      if (error) {
        throw new Error(`Failed to add Twitter content type: ${error.message}`);
      }

      return { 
        success: true, 
        message: `Twitter content type "${value}" added successfully` 
      };
    }),

  // Delete content type from enum (only if not in use)
  deleteContentType: adminProcedure
    .input(z.object({
      value: z.string(),
      force: z.boolean().default(false)
    }))
    .mutation(async ({ input }) => {
      const { value, force } = input;
      
      // Check if the type is in use
      const { data: usage, error: usageError } = await supabase
        .from('content_pieces')
        .select('id')
        .contains('content_types', [value]);

      if (usageError) {
        throw new Error(`Failed to check usage: ${usageError.message}`);
      }

      if (usage.length > 0 && !force) {
        throw new Error(`Cannot delete content type "${value}" as it is used in ${usage.length} content pieces. Use force to delete anyway.`);
      }

      // Use our custom disable function instead of trying to remove from enum
      const { data, error } = await supabase.rpc('disable_enum_value', {
        p_enum_type: 'content_type',
        p_enum_value: value.toLowerCase()
      });

      if (error) {
        throw new Error(`Failed to disable content type: ${error.message}`);
      }

      return { 
        success: true, 
        message: `Content type "${value}" has been disabled and will no longer appear in the interface.` 
      };
    }),

  // Delete content category from enum (only if not in use)
  deleteContentCategory: adminProcedure
    .input(z.object({
      value: z.string(),
      force: z.boolean().default(false)
    }))
    .mutation(async ({ input }) => {
      const { value, force } = input;
      
      // Attempting to delete content category: "${value}", force: ${force}`);
      
      // Check if the category is in use
      const { data: usage, error: usageError } = await supabase
        .from('content_pieces')
        .select('id')
        .contains('content_categories', [value]);

      // Category usage check result:`, { usage, usageError });

      if (usageError) {
        throw new Error(`Failed to check usage: ${usageError.message}`);
      }

      if (usage.length > 0 && !force) {
        throw new Error(`Cannot delete content category "${value}" as it is used in ${usage.length} content pieces. Use force to delete anyway.`);
      }

      // Use our custom disable function instead of trying to remove from enum
      const { data, error } = await supabase.rpc('disable_enum_value', {
        p_enum_type: 'content_category',
        p_enum_value: value.toLowerCase()
      });

      // disable_enum_value (category) result:`, { data, error });

      if (error) {
        throw new Error(`Failed to disable content category: ${error.message}`);
      }

      return { 
        success: true, 
        message: `Content category "${value}" has been disabled and will no longer appear in the interface.` 
      };
    }),

  // Delete Twitter content type from enum (only if not in use)
  deleteTwitterContentType: adminProcedure
    .input(z.object({
      value: z.string(),
      force: z.boolean().default(false)
    }))
    .mutation(async ({ input }) => {
      const { value, force } = input;
      
      // Attempting to delete Twitter content type: "${value}", force: ${force}`);
      
      // Check if the Twitter content type is in use
      const { data: usage, error: usageError } = await supabase
        .from('content_pieces')
        .select('id')
        .eq('twitter_content_type', value);

      // Twitter type usage check result:`, { usage, usageError });

      if (usageError) {
        throw new Error(`Failed to check usage: ${usageError.message}`);
      }

      if (usage.length > 0 && !force) {
        throw new Error(`Cannot delete Twitter content type "${value}" as it is used in ${usage.length} content pieces. Use force to delete anyway.`);
      }

      // Use our custom disable function instead of trying to remove from enum
      const { data, error } = await supabase.rpc('disable_enum_value', {
        p_enum_type: 'twitter_content_type',
        p_enum_value: value.toLowerCase()
      });

      // disable_enum_value (twitter) result:`, { data, error });

      if (error) {
        throw new Error(`Failed to disable Twitter content type: ${error.message}`);
      }

      return { 
        success: true, 
        message: `Twitter content type "${value}" has been disabled and will no longer appear in the interface.` 
      };
    }),

  // Remove a tag from all content pieces
  removeContentTag: adminProcedure
    .input(z.object({
      value: z.string(),
      force: z.boolean().default(false)
    }))
    .mutation(async ({ input }) => {
      const { value, force } = input;
      
      // Attempting to remove content tag: "${value}", force: ${force}`);
      
      // Check if the tag is in use
      const { data: usage, error: usageError } = await supabase
        .from('content_pieces')
        .select('id')
        .contains('content_tags', [value]);

      // Tag usage check result:`, { usage, usageError });

      if (usageError) {
        throw new Error(`Failed to check usage: ${usageError.message}`);
      }

      if (usage.length > 0 && !force) {
        throw new Error(`Cannot remove tag "${value}" as it is used in ${usage.length} content pieces. Use force to remove anyway.`);
      }

      // Remove the tag from all content pieces that have it
      const { data, error } = await supabase.rpc('remove_array_value', {
        p_table_name: 'content_pieces',
        p_column_name: 'content_tags',
        p_value_to_remove: value
      });

      if (error) {
        throw new Error(`Failed to remove tag: ${error.message}`);
      }

      return { 
        success: true, 
        message: `Tag "${value}" removed from ${data || 0} content pieces` 
      };
    }),
});