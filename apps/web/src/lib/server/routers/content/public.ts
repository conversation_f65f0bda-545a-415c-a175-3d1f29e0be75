import {
  publicProcedure,
  router,
} from "../../trpc";
import { supabase, type ContentPiece, type SearchResult } from "../../supabase";
import { z } from "zod";
import { sanitizeSearchInput } from "../../../security/sanitization";

export const publicContentRouter = router({
  // Get available content types and categories with usage counts
  getContentFilters: publicProcedure
    .query(async () => {
      // Use the existing RPC function to get filter metadata with counts
      const { data: filterData, error: filterError } = await supabase.rpc('get_filter_metadata', {
        filter_type: null // Get all filter types
      });

      if (filterError) {
        throw new Error(`Failed to fetch content filters: ${filterError.message}`);
      }

      // Process the data to extract content types and categories with usage counts
      const contentTypesMap = new Map<string, number>();
      const categoriesMap = new Map<string, number>();

      filterData?.forEach((item: any) => {
        if (item.type === 'content_types' && item.usage_count > 0) {
          contentTypesMap.set(item.value, item.usage_count);
        } else if (item.type === 'content_categories' && item.usage_count > 0) {
          categoriesMap.set(item.value, item.usage_count);
        }
      });

      // Convert to arrays, sort by usage count (descending), then by name
      const contentTypes = Array.from(contentTypesMap.entries())
        .sort((a, b) => b[1] - a[1] || a[0].localeCompare(b[0]))
        .map(([type]) => type);

      const categories = Array.from(categoriesMap.entries())
        .sort((a, b) => b[1] - a[1] || a[0].localeCompare(b[0]))
        .map(([category]) => category);

      return {
        contentTypes,
        categories,
        // Also return usage counts for potential use in UI
        contentTypesWithCounts: Array.from(contentTypesMap.entries()).map(([type, count]) => ({
          type,
          count
        })),
        categoriesWithCounts: Array.from(categoriesMap.entries()).map(([category, count]) => ({
          category,
          count
        }))
      };
    }),

  // Get Twitter Spaces (sorted by tune-in numbers)
  getTwitterSpaces: publicProcedure
    .input(z.object({
      page: z.number().default(1),
      limit: z.number().default(9)
    }))
    .query(async ({ input }) => {
      const offset = (input.page - 1) * input.limit;
      
      const { data, error, count } = await supabase
        .from('content_pieces')
        .select('*', { count: 'exact' })
        .eq('twitter_content_type', 'space')
        .order('twitter_impressions', { ascending: false })
        .range(offset, offset + input.limit - 1);

      if (error) {
        throw new Error(`Failed to fetch Twitter Spaces: ${error.message}`);
      }

      return {
        data: data as ContentPiece[],
        total: count || 0,
        page: input.page,
        totalPages: Math.ceil((count || 0) / input.limit)
      };
    }),

  // Get Marketing Case Studies (only content with marketing type)
  getMarketingCaseStudies: publicProcedure
    .input(z.object({
      page: z.number().default(1),
      limit: z.number().default(9)
    }))
    .query(async ({ input }) => {
      const offset = (input.page - 1) * input.limit;
      
      const { data, error, count } = await supabase
        .from('content_pieces')
        .select('*', { count: 'exact' })
        .contains('content_types', ['marketing'])
        .order('twitter_impressions', { ascending: false })
        .range(offset, offset + input.limit - 1);

      if (error) {
        throw new Error(`Failed to fetch marketing case studies: ${error.message}`);
      }

      return {
        data: data as ContentPiece[],
        total: count || 0,
        page: input.page,
        totalPages: Math.ceil((count || 0) / input.limit)
      };
    }),

  // Get Testimonials (only content with testimonials type)
  getTestimonials: publicProcedure
    .input(z.object({
      page: z.number().default(1),
      limit: z.number().default(9)
    }))
    .query(async ({ input }) => {
      const offset = (input.page - 1) * input.limit;
      
      const { data, error, count } = await supabase
        .from('content_pieces')
        .select('*', { count: 'exact' })
        .contains('content_types', ['testimonials'])
        .order('twitter_impressions', { ascending: false })
        .range(offset, offset + input.limit - 1);

      if (error) {
        throw new Error(`Failed to fetch testimonials: ${error.message}`);
      }

      return {
        data: data as ContentPiece[],
        total: count || 0,
        page: input.page,
        totalPages: Math.ceil((count || 0) / input.limit)
      };
    }),

  // Search all content
  searchContent: publicProcedure
    .input(z.object({
      query: z.string(),
      limit: z.number().default(20)
    }))
    .query(async ({ input }) => {
      const { data, error } = await supabase
        .rpc('search_content_pieces', {
          search_query: input.query,
          limit_count: input.limit
        });

      if (error) {
        throw new Error(`Search failed: ${error.message}`);
      }

      return data as SearchResult[];
    }),

  // Get all content with pagination
  getAllContent: publicProcedure
    .input(z.object({
      page: z.number().default(1),
      limit: z.number().default(20)
    }))
    .query(async ({ input }) => {
      const offset = (input.page - 1) * input.limit;
      
      const { data, error, count } = await supabase
        .from('content_pieces')
        .select('*', { count: 'exact' })
        .order('content_created_date', { ascending: false })
        .range(offset, offset + input.limit - 1);

      if (error) {
        throw new Error(`Failed to fetch content: ${error.message}`);
      }

      return {
        data: data as ContentPiece[],
        total: count || 0,
        page: input.page,
        totalPages: Math.ceil((count || 0) / input.limit)
      };
    }),

  // Get Twitter Tweets (sorted by impressions)
  getTwitterTweets: publicProcedure.query(async () => {
    const { data, error } = await supabase
      .from('content_pieces')
      .select('*')
      .eq('twitter_content_type', 'tweet')
      .order('twitter_impressions', { ascending: false })
      .limit(20);

    if (error) {
      throw new Error(`Failed to fetch Twitter tweets: ${error.message}`);
    }

    return data as ContentPiece[];
  }),

  // Get all Twitter content (Spaces + Tweets)
  getAllTwitterContent: publicProcedure
    .input(z.object({
      page: z.number().default(1),
      limit: z.number().default(9)
    }))
    .query(async ({ input }) => {
      const offset = (input.page - 1) * input.limit;
      
      // Use a simpler approach - get content with Twitter URLs or impressions
      let query = supabase
        .from('content_pieces')
        .select('*', { count: 'exact' });
      
      // Apply OR filter for Twitter content
      query = query.or('content_link.ilike.%x.com%,content_link.ilike.%twitter.com%,twitter_impressions.gt.0');
      
      const { data, error, count } = await query
        .order('twitter_impressions', { ascending: false })
        .range(offset, offset + input.limit - 1);

      if (error) {
        throw new Error(`Failed to fetch Twitter content: ${error.message}`);
      }

      return {
        data: data as ContentPiece[],
        total: count || 0,
        page: input.page,
        totalPages: Math.ceil((count || 0) / input.limit)
      };
    }),

  // Get content by category
  getContentByCategory: publicProcedure
    .input(z.object({
      category: z.string(),
      limit: z.number().default(20)
    }))
    .query(async ({ input }) => {
      const { data, error } = await supabase
        .from('content_pieces')
        .select('*')
        .contains('content_categories', [input.category])
        .order('twitter_impressions', { ascending: false })
        .limit(input.limit);

      if (error) {
        throw new Error(`Failed to fetch content by category: ${error.message}`);
      }

      return data as ContentPiece[];
    }),

  // Get content by type (twitter, marketing, etc.)
  getContentByType: publicProcedure
    .input(z.object({
      type: z.string(),
      limit: z.number().default(20)
    }))
    .query(async ({ input }) => {
      const { data, error } = await supabase
        .from('content_pieces')
        .select('*')
        .contains('content_types', [input.type])
        .order('twitter_impressions', { ascending: false })
        .limit(input.limit);

      if (error) {
        throw new Error(`Failed to fetch content by type: ${error.message}`);
      }

      return data as ContentPiece[];
    }),

  // Advanced filtered content with sorting
  getFilteredContent: publicProcedure
    .input(z.object({
      search: z.string().default(""),
      contentTypes: z.array(z.string()).default([]),
      categories: z.array(z.string()).default([]),
      sortBy: z.enum(["impressions", "date", "likes", "retweets"]).default("impressions"),
      sortOrder: z.enum(["desc", "asc"]).default("desc"),
      limit: z.number().default(30)
    }))
    .query(async ({ input }) => {
      console.log('[TRPC ROUTE] content.public.getFilteredContent:start', {
        search: input.search,
        contentTypesCount: input.contentTypes.length,
        categoriesCount: input.categories.length,
        sortBy: input.sortBy,
        sortOrder: input.sortOrder,
        limit: input.limit,
      });

      let query = supabase.from('content_pieces').select('*');

      // Collect all filter conditions into a single array for union (OR) logic
      const allConditions: string[] = [];

      // Add content type filter conditions
      if (input.contentTypes.length > 0) {
        input.contentTypes.forEach(type => {
          switch (type) {
            case "spaces":
              allConditions.push("twitter_content_type.eq.space");
              break;
            case "tweets":
              allConditions.push("twitter_content_type.eq.tweet");
              break;
            case "twitter":
              allConditions.push("content_link.ilike.%x.com%");
              allConditions.push("content_link.ilike.%twitter.com%");
              allConditions.push("twitter_impressions.gt.0");
              break;
            case "marketing":
            case "presskit":
            case "incubation":
            case "testimonials":
              allConditions.push(`content_types.cs.{${type}}`);
              break;
          }
        });
      }

      // Add category filter conditions
      if (input.categories.length > 0) {
        const categoryConditions = input.categories.map(category =>
          `content_categories.cs.{${category}}`
        );
        allConditions.push(...categoryConditions);
      }

      // Add search conditions to the unified filter
      if (input.search && input.search.length > 0) {
        const sanitizedSearch = sanitizeSearchInput(input.search);
        console.log('[TRPC ROUTE] content.public.getFilteredContent:sanitizedSearch', { sanitizedSearch });
        if (sanitizedSearch.length > 0) {
          // IMPORTANT: Do NOT apply ilike to array columns (e.g., content_account is text[])
          const searchConditions = [
            `content_title.ilike.%${sanitizedSearch}%`,
            `content_description.ilike.%${sanitizedSearch}%`,
          ];
          // Optional exact match for accounts (array) via contains; commented out to avoid over-filtering
          // searchConditions.push(`content_account.cs.{${sanitizedSearch}}`);
          allConditions.push(...searchConditions);
        }
      }

      console.log('[TRPC ROUTE] content.public.getFilteredContent:conditions', allConditions);

      // Apply all conditions with a single .or() call for union logic
      if (allConditions.length > 0) {
        query = query.or(allConditions.join(','));
      }

      // Apply sorting
      const sortColumn = input.sortBy === "date" ? "content_created_date" :
                        input.sortBy === "impressions" ? "twitter_impressions" :
                        input.sortBy === "likes" ? "twitter_likes" : "twitter_retweets";

      console.log('[TRPC ROUTE] content.public.getFilteredContent:sorting', { sortColumn, ascending: input.sortOrder === 'asc' });

      // Apply sorting with proper nulls handling
      query = query.order(sortColumn, { 
        ascending: input.sortOrder === "asc",
        nullsFirst: false  // Put null values at the end regardless of sort order
      });

      // Apply limit
      query = query.limit(input.limit);

      const { data, error } = await query;

      if (error) {
        console.error('[TRPC ROUTE] content.public.getFilteredContent:supabase-error', {
          message: error.message,
          details: (error as any).details,
          hint: (error as any).hint,
          code: (error as any).code,
          conditions: allConditions,
        });
        throw new Error(`Failed to fetch filtered content: ${error.message}`);
      }

      console.log('[TRPC ROUTE] content.public.getFilteredContent:success', { rows: data?.length ?? 0 });

      return data as ContentPiece[];
    }),

  // Get featured content for landing page
  getFeaturedContent: publicProcedure.query(async () => {
    const { data, error } = await supabase
      .from('content_pieces')
      .select('*')
      .order('twitter_impressions', { ascending: false })
      .limit(6);

    if (error) {
      throw new Error(`Failed to fetch featured content: ${error.message}`);
    }

    return data as ContentPiece[];
  }),
});