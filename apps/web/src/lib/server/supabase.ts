import { createClient } from '@supabase/supabase-js';
import { createServerClient } from '@supabase/ssr';
import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { getValidatedEnv } from '../security/env-validation';

// Get validated environment variables
const env = getValidatedEnv();
const supabaseUrl = env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = env.SUPABASE_ANON_KEY || env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

// Basic client for non-auth operations
export const supabase = createClient(supabaseUrl, supabaseKey);

// Server client for auth operations in Server Components
export async function createServerSupabaseClient() {
  const cookieStore = await cookies();
  
  return createServerClient(supabaseUrl, supabaseKey, {
    cookies: {
      getAll() {
        return cookieStore.getAll();
      },
      setAll(cookiesToSet) {
        try {
          cookiesToSet.forEach(({ name, value, options }) => {
            cookieStore.set({ name, value, ...options });  // Use object form, no overrides
          });
        } catch (error) {
          // The `setAll` method was called from a Server Component.
          // This can be ignored if you have middleware refreshing
          // user sessions.
          console.error('Error setting cookies in Server Component:', error);
        }
      },
    },
  });
}

// Middleware client for session refresh
export function createMiddlewareSupabaseClient(
  request: NextRequest,
  response: NextResponse
) {
  return createServerClient(supabaseUrl, supabaseKey, {
    cookies: {
      getAll() {
        return request.cookies.getAll();
      },
      setAll(cookiesToSet) {
        cookiesToSet.forEach(({ name, value }) => request.cookies.set(name, value));
        cookiesToSet.forEach(({ name, value, options }) =>
          response.cookies.set(name, value, options)
        );
      },
    },
  });
}

// Types for our content pieces
export interface ContentPiece {
  id: number;
  content_uuid: string;
  content_link: string;
  content_tags: string[];
  host: string;
  content_account: string[];
  content_created_date: string;
  content_types: string[];
  twitter_content_type: string | null;
  content_views: number;
  content_listeners: number;
  twitter_impressions: number;
  twitter_likes: number;
  twitter_retweets: number;
  content_follow_increase: number;
  content_title: string | null;
  content_description: string | null;
  content_categories: string[];
  screenshot_urls?: string[]; // UploadThing URLs for testimonial screenshots
  created_at: string;
  updated_at: string;
}

export interface SearchResult extends ContentPiece {
  rank: number;
}

// Types for groups feature
export interface Group {
  id: string;
  name: string;
  user_id: string;
  description: string | null;
  is_public: boolean;
  created_at: string;
  updated_at: string;
}

export interface GroupContent {
  id: number;
  group_id: string;
  content_id: number;
  position: number;
  added_at: string;
}

export interface GroupWithContent extends Group {
  content: (ContentPiece & { position: number; added_at: string })[];
  content_count: number;
}