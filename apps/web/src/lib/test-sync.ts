import { parseCsvData, transformCsvRow, type CsvRow } from './services/csv-sync';

// Test with a sample of the actual CSV data
const sampleCsvData = `Client Name,Show Topic,Show Type,Date,Show Title,Show Link,Report Link,Views / Listeners,Impressions,Follow Increase
ESGold,Web2,69X Minutes,13/07/2025,"🚨69 𝕏 MINUTES w/ TRUMP FAMILY, RAND PAUL & SEC HEGSETH: SHAKING UP THE SWAMP | EP. 12",https://x.com/MarioNawfal/status/1944501674422489406,https://drive.google.com/file/d/1Dv0z9wynuM4Rvw0CG_CSXON2R5nevZew/view,-,1.1 M,~400
Lumerin Protocol ,AI,Video on RT,02/12/2024,Bitcoin Hashrate Hits ATH! #CryptoDaily w/@HelloLumerin,https://x.com/i/broadcasts/1RDGlyvpAVEJL,https://drive.google.com/file/d/1DEdrx7m2zUQJ9NV7MvaOy9pwpKkzm1l_/view?usp=drive_link,103.1k,310.7K,800
Solcat,Memecoin,SS on M,18/11/2024,Memecoin Mania on Solana #CryptoDaily w/@SOLCATGAME,https://x.com/i/spaces/1mrxmMRMOBDxy,https://drive.google.com/file/d/1IAdA7kk184_cVBXBGDmsVprtWySfcv1k/view,87.3K,296.7k,"5,000"`;

export async function testSync(): Promise<void> {
  try {
    console.log('🧪 Testing CSV parsing and transformation...');
    
    // Test CSV parsing
    const contentPieces = parseCsvData(sampleCsvData);
    
    console.log(`Parsed ${contentPieces.length} content pieces:`);
    
    contentPieces.forEach((piece, index) => {
      console.log(`\n--- Content Piece ${index + 1} ---`);
      console.log('Link:', piece.content_link);
      console.log('Host:', piece.host);
      console.log('Client Name:', piece.content_account);
      console.log('Content Types:', piece.content_types);
      console.log('Twitter Content Type:', piece.twitter_content_type);
      console.log('Categories:', piece.content_categories);
      console.log('Title:', piece.content_title);
      console.log('Views:', piece.content_views);
      console.log('Impressions:', piece.twitter_impressions);
      console.log('Follow Increase:', piece.content_follow_increase);
      console.log('Date:', piece.content_created_date);
    });
    
    console.log('\n✅ CSV parsing test completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  }
}

// Run test if this file is executed directly
if (require.main === module) {
  testSync().catch(console.error);
}