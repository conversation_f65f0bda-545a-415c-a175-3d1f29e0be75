import { SheetsSyncService } from './services/sheets-sync';

async function testRealSync() {
  try {
    console.log('🚀 Testing REAL sync with Joey.csv to Supabase...');
    
    // Create sync service instance
    const syncService = new SheetsSyncService();
    
    // REAL sync - this will actually insert data
    console.log('\n--- REAL SYNC (Writing to Database) ---');
    const realResult = await syncService.sync({
      useGoogleSheets: false,
      csvFilePath: '/Users/<USER>/Downloads/Coding/IBC/ibc-final-dashboard/docs/Joey.csv',
      dryRun: false // REAL sync this time
    });
    
    console.log('🎉 Real sync completed!');
    console.log('Results:', realResult);
    
    // Show detailed results
    console.log('\n--- SYNC STATISTICS ---');
    console.log(`✅ Created: ${realResult.created}`);
    console.log(`🔄 Updated: ${realResult.updated}`);
    console.log(`⏭️  Skipped: ${realResult.skipped}`);
    console.log(`❌ Errors: ${realResult.errors.length}`);
    console.log(`📊 Total Processed: ${realResult.totalProcessed}`);
    
    if (realResult.errors.length > 0) {
      console.log('\n--- ERRORS ---');
      realResult.errors.forEach((error, i) => {
        console.log(`${i + 1}. ${error}`);
      });
    }
    
    return realResult;
    
  } catch (error) {
    console.error('❌ Real sync failed:', error);
    throw error;
  }
}

// Run the test
testRealSync();