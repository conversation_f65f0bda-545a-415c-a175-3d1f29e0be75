import { type NextRequest, NextResponse } from 'next/server';
import { updateSession } from '@/utils/supabase/middleware';

export async function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;
  const fullUrl = request.url;
  
  console.log('🔍 [MIDDLEWARE] Processing request:', { pathname, fullUrl });
  
  // Multiple checks to ensure cron routes are properly detected
  const isCronRoute = pathname.startsWith('/api/cron') || 
                     pathname.includes('/api/cron/') || 
                     fullUrl.includes('/api/cron/');
  
  if (isCronRoute) {
    console.log('🛑 [MIDDLEWARE] Skipping Supabase session update for cron route:', pathname);
    return NextResponse.next({ request });
  }
  
  console.log('🔍 [MIDDLEWARE] Proceeding with updateSession for non-cron route:', pathname);
  return await updateSession(request);
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * Feel free to modify this pattern to include more paths.
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
};