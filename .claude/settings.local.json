{"permissions": {"allow": ["mcp__supabase__search_docs", "mcp__supabase__list_projects", "mcp__supabase__execute_sql", "Bash(bun add:*)", "<PERSON><PERSON>(touch:*)", "mcp__supabase__get_anon_key", "Bash(bunx turbo:*)", "Bash(rm:*)", "Bash(bun build:*)", "Bash(gh repo create:*)", "Bash(grep:*)", "<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(split:*)", "Bash(ls:*)", "<PERSON><PERSON>(sed:*)", "Bash(awk:*)", "Bash(for i in {3..7})", "Bash(do echo \"=== Statement $i ===\")", "<PERSON><PERSON>(cat:*)", "Bash(echo)", "Bash(done)", "Bash(bun install:*)", "Bash(bun:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git push:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "mcp__supabase__list_tables", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(timeout:*)", "mcp__supabase__apply_migration", "Bash(git rm:*)", "<PERSON><PERSON>(mv:*)", "mcp__supabase__get_project", "Bash(find:*)", "Bash(node:*)", "Bash(cp:*)"], "deny": []}}