# Plan Checklist

Summary: Fix the filtered content query error (text[] ~~* unknown), add robust logging, and outline follow-ups for search UX and Supabase session issues.

## Checklist
- [x] Reproduce and triage error: `operator does not exist: text[] ~~* unknown` in content.public.getFilteredContent
- [x] Identify root cause: using ilike on text[] column `content_account`
- [x] Patch query to avoid ilike on array columns; restrict search to text columns (title, description)
- [x] Add robust console logging for inputs, built conditions, sorting, and Supabase errors
- [ ] Optional enhancement: support account search by exact handle using array contains (`content_account.cs.{handle}`) when query looks like a handle (e.g., starts with @)
- [ ] Optional enhancement: richer search across arrays via RPC (e.g., lower(array_to_string(content_account, ',')) ilike pattern)
- [ ] Verify manually in dev: search for terms like "Live" and confirm results + no 500s
- [ ] Investigate Supabase session issue in tRPC context (cookies only show code-verifier; session missing). Ensure SSR client usage where needed
- [ ] Review logs after verification and iterate

## Files touched
- apps/web/src/lib/server/routers/content/public.ts — updated search conditions and added logging

## Notes
- We purposely avoided ilike against array columns. PostgREST expects array ops like `cs`, `ov`, etc. For fuzzy matching within arrays, implement a dedicated SQL function and call via RPC.
- Logging tags standardized under `[TRPC ROUTE] content.public.getFilteredContent:*` for easy grep.

